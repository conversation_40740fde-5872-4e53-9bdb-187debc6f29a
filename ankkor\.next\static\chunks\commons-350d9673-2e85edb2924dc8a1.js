"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4754],{82372:function(n,t,e){e.d(t,{Bk:function(){return Z},CP:function(){return Y},Dg:function(){return z},Id:function(){return ni},Iz:function(){return no},ML:function(){return ns},Op:function(){return nr},Xp:function(){return M},Xq:function(){return nt},dv:function(){return nn},gk:function(){return nu},h2:function(){return ne},mJ:function(){return na},s3:function(){return nc},wv:function(){return nl},xu:function(){return nm}});var r=e(45008),o=e(34206),a=e(87466),i=e(40257);function s(){let n=(0,r._)(["\n  fragment ProductFields on Product {\n    id\n    databaseId\n    name\n    slug\n    description\n    shortDescription\n    type\n    image {\n      sourceUrl\n      altText\n    }\n    galleryImages {\n      nodes {\n        sourceUrl\n        altText\n      }\n    }\n    ... on SimpleProduct {\n      price\n      regularPrice\n      salePrice\n      onSale\n      stockStatus\n      stockQuantity\n    }\n    ... on VariableProduct {\n      price\n      regularPrice\n      salePrice\n      onSale\n      stockStatus\n      stockQuantity\n      attributes {\n        nodes {\n          name\n          options\n        }\n      }\n    }\n  }\n"]);return s=function(){return n},n}function u(){let n=(0,r._)(["\n  fragment VariableProductWithVariations on VariableProduct {\n    attributes {\n      nodes {\n        name\n        options\n      }\n    }\n    variations {\n      nodes {\n        id\n        databaseId\n        name\n        price\n        regularPrice\n        salePrice\n        stockStatus\n        stockQuantity\n        attributes {\n          nodes {\n            name\n            value\n          }\n        }\n      }\n    }\n  }\n"]);return u=function(){return n},n}function d(){let n=(0,r._)(["\n  query GetProducts(\n    $first: Int\n    $after: String\n    $where: RootQueryToProductConnectionWhereArgs\n  ) {\n    products(first: $first, after: $after, where: $where) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        ...ProductFields\n        ... on VariableProduct {\n          ...VariableProductWithVariations\n        }\n      }\n    }\n  }\n  ","\n  ","\n"]);return d=function(){return n},n}function c(){let n=(0,r._)(["\n  query GetProductBySlug($slug: ID!) {\n    product(id: $slug, idType: SLUG) {\n      ...ProductFields\n      ... on VariableProduct {\n        ...VariableProductWithVariations\n      }\n    }\n  }\n  ","\n  ","\n"]);return c=function(){return n},n}function l(){let n=(0,r._)(["\n  query GetProductBySlugWithTags($slug: ID!) {\n    product(id: $slug, idType: SLUG) {\n      ...ProductFields\n      ... on VariableProduct {\n        ...VariableProductWithVariations\n      }\n      productTags {\n        nodes {\n          id\n          name\n          slug\n        }\n      }\n      productCategories {\n        nodes {\n          id\n          name\n          slug\n        }\n      }\n    }\n  }\n  ","\n  ","\n"]);return l=function(){return n},n}function p(){let n=(0,r._)(["\n  query GetCategories(\n    $first: Int\n    $after: String\n    $where: RootQueryToProductCategoryConnectionWhereArgs\n  ) {\n    productCategories(first: $first, after: $after, where: $where) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        count\n        image {\n          sourceUrl\n          altText\n        }\n      }\n    }\n  }\n"]);return p=function(){return n},n}function m(){let n=(0,r._)(["\n  query GetAllProducts($first: Int = 20) {\n    products(first: $first) {\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        shortDescription\n        productCategories {\n          nodes {\n            id\n            name\n            slug\n          }\n        }\n        ... on SimpleProduct {\n          price\n          regularPrice\n          salePrice\n          onSale\n          stockStatus\n          stockQuantity\n        }\n        ... on VariableProduct {\n          price\n          regularPrice\n          salePrice\n          onSale\n          stockStatus\n          variations {\n            nodes {\n              stockStatus\n              stockQuantity\n            }\n          }\n        }\n        image {\n          id\n          sourceUrl\n          altText\n        }\n        galleryImages {\n          nodes {\n            id\n            sourceUrl\n            altText\n          }\n        }\n        ... on VariableProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n        }\n        ... on SimpleProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n        }\n      }\n    }\n  }\n"]);return m=function(){return n},n}function g(){let n=(0,r._)(["\n  query GetProductsByCategory($slug: ID!, $first: Int = 20) {\n    productCategory(id: $slug, idType: SLUG) {\n      id\n      name\n      slug\n      description\n      products(first: $first) {\n        nodes {\n          id\n          databaseId\n          name\n          slug\n          ... on SimpleProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n          }\n          ... on VariableProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n          }\n          image {\n            id\n            sourceUrl\n            altText\n          }\n        }\n      }\n    }\n  }\n"]);return g=function(){return n},n}function f(){let n=(0,r._)(["\n  query GetAllCategories($first: Int = 20) {\n    productCategories(first: $first) {\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        count\n        image {\n          sourceUrl\n          altText\n        }\n        children {\n          nodes {\n            id\n            name\n            slug\n          }\n        }\n      }\n    }\n  }\n"]);return f=function(){return n},n}function y(){let n=(0,r._)(["\n  query GetCart {\n    cart {\n      contents {\n        nodes {\n          key\n          product {\n            node {\n              id\n              databaseId\n              name\n              slug\n              type\n              image {\n                sourceUrl\n                altText\n              }\n            }\n          }\n          variation {\n            node {\n              id\n              databaseId\n              name\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n          quantity\n          total\n        }\n      }\n      subtotal\n      total\n      totalTax\n      isEmpty\n    }\n  }\n"]);return y=function(){return n},n}function h(){let n=(0,r._)(['\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: "login"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        nicename\n        nickname\n        username\n      }\n    }\n  }\n']);return h=function(){return n},n}function v(){let n=(0,r._)(["\n  query GetCart {\n    cart {\n      contents {\n        nodes {\n          key\n          product {\n            node {\n              id\n              databaseId\n              name\n              slug\n              type\n              image {\n                sourceUrl\n                altText\n              }\n            }\n          }\n          variation {\n            node {\n              id\n              databaseId\n              name\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n          quantity\n          total\n        }\n      }\n      subtotal\n      total\n      totalTax\n      isEmpty\n      contentsCount\n    }\n  }\n"]);return v=function(){return n},n}function P(){let n=(0,r._)(['\n  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {\n    addToCart(\n      input: {\n        clientMutationId: "addToCart"\n        productId: $productId\n        variationId: $variationId\n        quantity: $quantity\n        extraData: $extraData\n      }\n    ) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                databaseId\n                name\n                slug\n                type\n                image {\n                  sourceUrl\n                  altText\n                }\n              }\n            }\n            variation {\n              node {\n                id\n                databaseId\n                name\n                attributes {\n                  nodes {\n                    name\n                    value\n                  }\n                }\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n        contentsCount\n      }\n    }\n  }\n']);return P=function(){return n},n}function I(){let n=(0,r._)(["\n  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {\n    removeItemsFromCart(input: { keys: $keys, all: $all }) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                databaseId\n                name\n                slug\n                type\n                image {\n                  sourceUrl\n                  altText\n                }\n              }\n            }\n            variation {\n              node {\n                id\n                databaseId\n                name\n                attributes {\n                  nodes {\n                    name\n                    value\n                  }\n                }\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n      }\n    }\n  }\n"]);return I=function(){return n},n}function b(){let n=(0,r._)(["\n  query GetShippingMethods {\n    shippingMethods {\n      nodes {\n        id\n        title\n        description\n        cost\n      }\n    }\n  }\n"]);return b=function(){return n},n}function C(){let n=(0,r._)(["\n  query GetPaymentGateways {\n    paymentGateways {\n      nodes {\n        id\n        title\n        description\n        enabled\n      }\n    }\n  }\n"]);return C=function(){return n},n}function w(){let n=(0,r._)(["\n      query GetProductById($id: ID!) {\n        product(id: $id, idType: DATABASE_ID) {\n          id\n          databaseId\n          name\n          slug\n          description\n          shortDescription\n          productCategories {\n            nodes {\n              id\n              name\n              slug\n            }\n          }\n          ... on SimpleProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            stockQuantity\n          }\n          ... on VariableProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            variations {\n              nodes {\n                stockStatus\n                stockQuantity\n              }\n            }\n          }\n          image {\n            id\n            sourceUrl\n            altText\n          }\n        }\n      }\n    "]);return w=function(){return n},n}function S(){let n=(0,r._)(["\n    query SearchProducts($query: String!, $first: Int) {\n      products(first: $first, where: { search: $query }) {\n        nodes {\n          id\n          databaseId\n          name\n          slug\n          price\n          image {\n            sourceUrl\n            altText\n          }\n          shortDescription\n        }\n        pageInfo {\n          hasNextPage\n          endCursor\n        }\n      }\n    }\n  "]);return S=function(){return n},n}function T(){let n=(0,r._)(["\n    query GetProduct($id: ID!) {\n      product(id: $id, idType: DATABASE_ID) {\n        id\n        databaseId\n        name\n        slug\n        description\n        shortDescription\n        price\n        regularPrice\n        salePrice\n        onSale\n        stockStatus\n        stockQuantity\n        image {\n          sourceUrl\n          altText\n        }\n        galleryImages {\n          nodes {\n            sourceUrl\n            altText\n          }\n        }\n        ... on SimpleProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n          price\n          regularPrice\n          salePrice\n        }\n        ... on VariableProduct {\n          price\n          regularPrice\n          salePrice\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n          variations {\n            nodes {\n              id\n              databaseId\n              name\n              price\n              regularPrice\n              salePrice\n              stockStatus\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  "]);return T=function(){return n},n}function k(){let n=(0,r._)(["\n  mutation CreateCustomer($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n      }\n      authToken\n      refreshToken\n    }\n  }\n"]);return k=function(){return n},n}function $(){let n=(0,r._)(["\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      clientMutationId\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n      customerUserErrors {\n        field\n        message\n      }\n    }\n  }\n"]);return $=function(){return n},n}function N(){let n=(0,r._)(["\n  query GetCustomer {\n    customer {\n      id\n      databaseId\n      email\n      firstName\n      lastName\n      displayName\n      username\n      role\n      date\n      modified\n      isPayingCustomer\n      orderCount\n      totalSpent\n      billing {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n        email\n        phone\n      }\n      shipping {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n      }\n      orders(first: 50) {\n        nodes {\n          id\n          databaseId\n          date\n          status\n          total\n          subtotal\n          totalTax\n          shippingTotal\n          discountTotal\n          paymentMethodTitle\n          customerNote\n          billing {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n            email\n            phone\n          }\n          shipping {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n          }\n          lineItems {\n            nodes {\n              product {\n                node {\n                  id\n                  name\n                  slug\n                  image {\n                    sourceUrl\n                    altText\n                  }\n                }\n              }\n              variation {\n                node {\n                  id\n                  name\n                  attributes {\n                    nodes {\n                      name\n                      value\n                    }\n                  }\n                }\n              }\n              quantity\n              total\n              subtotal\n              totalTax\n            }\n          }\n          shippingLines {\n            nodes {\n              methodTitle\n              total\n            }\n          }\n          feeLines {\n            nodes {\n              name\n              total\n            }\n          }\n          couponLines {\n            nodes {\n              code\n              discount\n            }\n          }\n        }\n      }\n      downloadableItems {\n        nodes {\n          name\n          downloadId\n          downloadsRemaining\n          accessExpires\n          product {\n            node {\n              id\n              name\n            }\n          }\n        }\n      }\n      metaData {\n        key\n        value\n      }\n    }\n  }\n"]);return N=function(){return n},n}function _(){let n=(0,r._)(["\n  mutation CreateAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return _=function(){return n},n}function x(){let n=(0,r._)(["\n  mutation UpdateAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return x=function(){return n},n}function U(){let n=(0,r._)(["\n  mutation DeleteAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return U=function(){return n},n}function q(){let n=(0,r._)(["\n  mutation SetDefaultAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n"]);return q=function(){return n},n}function E(){let n=(0,r._)(["\n  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {\n    updateItemQuantities(input: $input) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                name\n                price\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n      }\n    }\n  }\n"]);return E=function(){return n},n}let D={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:i.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},A=null,G=()=>sessionStorage.getItem("woo-session-token")||A,V=n=>{A=n,n?sessionStorage.setItem("woo-session-token",n):sessionStorage.removeItem("woo-session-token")},Q=i.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",O=new o.g6(Q,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),R=(0,o.Ps)(s()),F=(0,o.Ps)(u()),L=(0,o.Ps)(d(),R,F);async function M(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{return(await W(L,{first:n.first||12,after:n.after||null,where:n.where||{}},["products"],60)).products}catch(n){return console.error("Error fetching products:",n),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function W(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};arguments.length>2&&void 0!==arguments[2]&&arguments[2],arguments.length>3&&void 0!==arguments[3]&&arguments[3];try{{let e={"Content-Type":"application/json"},r=G();r&&(e["woocommerce-session"]="Session ".concat(r));let o={method:"POST",headers:e,body:JSON.stringify({query:n,variables:t})},a=await fetch("/api/graphql",o);if(!a.ok)throw Error("GraphQL API responded with status ".concat(a.status));let i=a.headers.get("woocommerce-session");if(i){let n=i.replace("Session ","");V(n)}let{data:s,errors:u}=await a.json();if(u)throw console.error("GraphQL Errors:",u),Error(u[0].message);return s}}catch(n){throw console.error("Error fetching from WooCommerce:",n),n}}async function B(n){let{query:t,variables:e}=n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3,a=0,i=null;for(;a<r;)try{return await W(t,e,[],0)}catch(n){i=n,++a<r&&(console.log("Retrying request (".concat(a,"/").concat(r,") after ").concat(o,"ms")),await new Promise(n=>setTimeout(n,o)),o*=2)}throw console.error("Failed after ".concat(r," attempts:"),i),i}(0,o.Ps)(c(),R,F),(0,o.Ps)(l(),R,F),(0,o.Ps)(p());let j=(0,o.Ps)(m());(0,o.Ps)(g());let H=(0,o.Ps)(f());(0,o.Ps)(y()),(0,o.Ps)(h());let K=(0,o.Ps)(v()),J=(0,o.Ps)(P()),X=(0,o.Ps)(I());async function z(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;try{var t;let e=await B({query:j,variables:{first:n}});return(null==e?void 0:null===(t=e.products)||void 0===t?void 0:t.nodes)||[]}catch(n){return console.error("Error fetching all products:",n),[]}}async function Y(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=await B({query:H,variables:{first:n.first||20,after:n.after||null,where:n.where||{}}});return{nodes:t.productCategories.nodes,pageInfo:t.productCategories.pageInfo}}catch(n){return console.error("Error fetching categories:",n),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function Z(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];try{if(0===n.length)return{contents:{nodes:[]},subtotal:"0",total:"0",totalTax:"0",isEmpty:!0,contentsCount:0};let t=n[0],e=await nt("",[t]);if(n.length>1){for(let t=1;t<n.length;t++)await nt("",[n[t]]);return await nn()}return e}catch(n){throw console.error("Error creating cart:",n),n}}async function nn(){try{let n=await B({query:K,variables:{}});return(null==n?void 0:n.cart)||null}catch(n){return console.error("Error fetching cart:",n),null}}async function nt(n,t){try{if(0===t.length)throw Error("No items provided to add to cart");let n=t[0],e={productId:parseInt(n.productId),quantity:n.quantity||1,variationId:n.variationId?parseInt(n.variationId):null,extraData:null};console.log("Adding to cart with variables:",e);let r=await B({query:J,variables:e});return console.log("Add to cart response:",r),r.addToCart.cart}catch(n){throw console.error("Error adding items to cart:",n),n}}async function ne(n,t){try{var e;let n=await B({query:X,variables:{keys:t,all:!1}});return(null==n?void 0:null===(e=n.removeItemsFromCart)||void 0===e?void 0:e.cart)||null}catch(n){throw console.error("Error removing items from cart:",n),n}}function nr(n){var t,e,r,o,a,i,s,u,d,c;if(!n)return null;let l=!!(null===(e=n.variations)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.length),p={minVariantPrice:{amount:n.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:n.price||"0",currencyCode:"INR"}};if(l&&(null===(o=n.variations)||void 0===o?void 0:null===(r=o.nodes)||void 0===r?void 0:r.length)>0){let t=n.variations.nodes.map(n=>parseFloat(n.price||"0")).filter(n=>!isNaN(n));t.length>0&&(p={minVariantPrice:{amount:String(Math.min(...t)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...t)),currencyCode:"INR"}})}let m=no(n),g=(null===(i=n.variations)||void 0===i?void 0:null===(a=i.nodes)||void 0===a?void 0:a.map(n=>{var t,e;return{id:n.id,title:n.name,price:{amount:n.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===n.stockStatus,selectedOptions:(null===(e=n.attributes)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.map(n=>({name:n.name,value:n.value})))||[],sku:n.sku||"",image:n.image?{url:n.image.sourceUrl,altText:n.image.altText||""}:null}}))||[],f=(null===(u=n.attributes)||void 0===u?void 0:null===(s=u.nodes)||void 0===s?void 0:s.map(n=>({name:n.name,values:n.options||[]})))||[],y=(null===(c=n.productCategories)||void 0===c?void 0:null===(d=c.nodes)||void 0===d?void 0:d.map(n=>({handle:n.slug,title:n.name})))||[],h={};return n.metafields&&n.metafields.forEach(n=>{h[n.key]=n.value}),{id:n.id,handle:n.slug,title:n.name,description:n.description||"",descriptionHtml:n.description||"",priceRange:p,options:f,variants:g,images:m,collections:y,availableForSale:"OUT_OF_STOCK"!==n.stockStatus,metafields:h,_originalWooProduct:n}}function no(n){var t,e;let r=[];return n.image&&r.push({url:n.image.sourceUrl,altText:n.image.altText||n.name||""}),(null===(e=n.galleryImages)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.length)&&n.galleryImages.nodes.forEach(t=>{n.image&&t.sourceUrl===n.image.sourceUrl||r.push({url:t.sourceUrl,altText:t.altText||n.name||""})}),r}(0,o.Ps)(b()),(0,o.Ps)(C());let na=function(n,t,e){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";if(!n||!n.metafields)return r;if(e){let o="".concat(e,":").concat(t);return n.metafields[o]||r}return n.metafields[t]||r};function ni(n){var t,e,r,o;if(!n)return null;let a=(null===(e=n.contents)||void 0===e?void 0:null===(t=e.nodes)||void 0===t?void 0:t.map(n=>{var t,e,r,o;let a=null===(t=n.product)||void 0===t?void 0:t.node,i=null===(e=n.variation)||void 0===e?void 0:e.node;return{id:n.key,quantity:n.quantity,merchandise:{id:(null==i?void 0:i.id)||(null==a?void 0:a.id),title:(null==i?void 0:i.name)||(null==a?void 0:a.name),product:{id:null==a?void 0:a.id,handle:null==a?void 0:a.slug,title:null==a?void 0:a.name,image:(null==a?void 0:a.image)?{url:null==a?void 0:a.image.sourceUrl,altText:(null==a?void 0:a.image.altText)||""}:null},selectedOptions:(null==i?void 0:null===(o=i.attributes)||void 0===o?void 0:null===(r=o.nodes)||void 0===r?void 0:r.map(n=>({name:n.name,value:n.value})))||[]},cost:{totalAmount:{amount:n.total||"0",currencyCode:"USD"}}}}))||[],i=(null===(o=n.appliedCoupons)||void 0===o?void 0:null===(r=o.nodes)||void 0===r?void 0:r.map(n=>({code:n.code,amount:n.discountAmount||"0"})))||[],s=a.reduce((n,t)=>n+t.quantity,0);return{id:n.id,checkoutUrl:"",totalQuantity:s,cost:{subtotalAmount:{amount:n.subtotal||"0",currencyCode:"USD"},totalAmount:{amount:n.total||"0",currencyCode:"USD"}},lines:a,discountCodes:i}}function ns(n){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],e="".concat(D.storeUrl,"/checkout"),r=n?"?cart=".concat(n):"",o="";return t||(o="".concat(r?"&":"?","guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1")),"".concat(e).concat(r).concat(o)}async function nu(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60;try{if(!n||"undefined"===n||"null"===n)return console.warn("Invalid product ID format: ".concat(n,", returning fallback product")),nd(n);let e=await (0,a.xh)(n),r=(0,o.Ps)(w());try{let o=await W(r,{id:e},["product-".concat(e),"products","inventory"],t);if(!(null==o?void 0:o.product))return console.warn("No product found with ID: ".concat(n,", returning fallback product")),nd(n);return o.product}catch(t){return console.error("Error fetching product with ID ".concat(n,":"),t),nd(n)}}catch(t){return console.error("Error in getProductById for ID ".concat(n,":"),t),nd(n)}}function nd(n){return{id:n,databaseId:0,name:"Product Not Found",slug:"product-not-found",description:"This product is no longer available.",shortDescription:"Product not found",price:"0.00",regularPrice:"0.00",salePrice:null,onSale:!1,stockStatus:"OUT_OF_STOCK",stockQuantity:0,image:{id:null,sourceUrl:"/placeholder-product.jpg",altText:"Product not found"},productCategories:{nodes:[]}}}async function nc(n){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e="number"==typeof t?t:t.first||10,r=(0,o.Ps)(S());try{let t=await O.request(r,{query:n,first:e});return(null==t?void 0:t.products)||{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}catch(n){return console.error("Error searching products:",n),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function nl(n){let t=(0,o.Ps)(T());try{return(await O.request(t,{id:n})).product}catch(n){throw console.error("Error fetching product:",n),Error("Failed to fetch product")}}(0,o.Ps)(k()),(0,o.Ps)($()),(0,o.Ps)(N()),(0,o.Ps)(_()),(0,o.Ps)(x()),(0,o.Ps)(U()),(0,o.Ps)(q());let np=(0,o.Ps)(E());async function nm(n){try{return(await B({query:np,variables:{input:{items:n}}})).updateItemQuantities.cart}catch(n){throw console.error("Error updating cart:",n),n}}}}]);