(()=>{var e={};e.id=6091,e.ids=[6091],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},44153:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>g,pages:()=>f,routeModule:()=>h,tree:()=>p});var o=r(15100);r(52617),r(12523);var a=r(23191),i=r(88716),s=r(37922),c=r.n(s),u=r(95231),d={};for(let e in u)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>u[e]);r.d(t,d);var l=e([o]);o=(l.then?(await l)():l)[0];let p=["",{children:["category",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15100)),"E:\\ankkorwoo\\ankkor\\src\\app\\category\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],f=["E:\\ankkorwoo\\ankkor\\src\\app\\category\\[slug]\\page.tsx"],g="/category/[slug]/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/category/[slug]/page",pathname:"/category/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});n()}catch(e){n(e)}})},35303:()=>{},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return i},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),o=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class i extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return c},getRedirectStatusCodeFromError:function(){return g},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return l},permanentRedirect:function(){return d},redirect:function(){return u}});let o=r(54580),a=r(72934),i=r(8586),s="NEXT_REDIRECT";function c(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r+";";let a=o.requestAsyncStorage.getStore();return a&&(n.mutableCookies=a.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function l(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,o]=e.digest.split(";",4),a=Number(o);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(a)&&a in i.RedirectStatusCode}function p(e){return l(e)?e.digest.split(";",3)[2]:null}function f(e){if(!l(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function g(e){if(!l(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15100:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>u,generateMetadata:()=>c});var o=r(19510);r(71159);var a=r(58585),i=r(19910),s=e([i]);async function c({params:e}){let{slug:t}=e,r=await (0,i.CP)(),n=r.nodes?.find(e=>e.slug===t);return n?{title:`${n.name} | Ankkor`,description:n.description||`Browse our collection of ${n.name.toLowerCase()} products.`}:{title:"Category Not Found | Ankkor",description:"The requested category could not be found."}}async function u({params:e}){let{slug:t}=e,r=await (0,i.CP)(),n=r.nodes?.find(e=>e.slug===t);n||(0,a.notFound)();let s=(await (0,i.Xp)({first:12,where:{categoryIn:[n.slug]}})).nodes||[];return(0,o.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,o.jsxs)("header",{className:"mb-8 text-center",children:[o.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27] mb-2",children:n.name}),n.description&&o.jsx("p",{className:"text-[#8a8778] max-w-2xl mx-auto",children:n.description}),(0,o.jsxs)("p",{className:"text-[#5c5c52] mt-2",children:[n.count," products"]})]}),0===s.length?o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-gray-500",children:"No products found in this category."})}):o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6",children:s.map(e=>o.jsx("div",{className:"product-card",children:(0,o.jsxs)("a",{href:`/product/${e.slug}`,className:"block",children:[e.image&&o.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden mb-4",children:o.jsx("img",{src:e.image.sourceUrl,alt:e.name,className:"w-full h-full object-cover"})}),o.jsx("h3",{className:"text-lg font-medium",children:e.name}),(0,o.jsxs)("p",{className:"text-gray-600",children:["$",parseFloat(e.price).toFixed(2)]})]})},e.id))})]})}i=(s.then?(await s)():s)[0],n()}catch(e){n(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1056,4766,4868,8702,9910],()=>r(44153));module.exports=n})();