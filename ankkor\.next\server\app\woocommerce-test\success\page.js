(()=>{var e={};e.id=6232,e.ids=[6232],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},85051:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l}),t(38958),t(52617),t(12523);var r=t(23191),a=t(88716),o=t(37922),n=t.n(o),c=t(95231),i={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);t.d(s,i);let l=["",{children:["woocommerce-test",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38958)),"E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-test\\success\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-test\\success\\page.tsx"],u="/woocommerce-test/success/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/woocommerce-test/success/page",pathname:"/woocommerce-test/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},44928:(e,s,t)=>{Promise.resolve().then(t.bind(t,29099))},29099:(e,s,t)=>{"use strict";t.a(e,async(e,r)=>{try{t.r(s),t.d(s,{default:()=>l});var a=t(10326),o=t(17577),n=t(90434),c=t(61296),i=e([c]);function l(){let[e,s]=(0,o.useState)(null),[t,r]=(0,o.useState)(!0);return a.jsx("div",{className:"container mx-auto py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Authentication Successful"}),t?a.jsx("p",{className:"text-center",children:"Loading user data..."}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"p-4 bg-green-50 border border-green-200 text-green-700",children:a.jsx("p",{className:"font-medium",children:"Successfully authenticated!"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h2",{className:"text-lg font-medium",children:"User Information"}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Name:"})," ",e.firstName," ",e.lastName]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Email:"})," ",e.email]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"ID:"})," ",e.id]})]})]}):a.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 text-yellow-700",children:a.jsx("p",{children:"No user data found. Authentication may have failed."})}),a.jsx("div",{className:"mt-8 flex justify-center",children:a.jsx(n.default,{href:"/woocommerce-test",className:"bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#3c3c37]",children:"Back to Test Page"})})]})})}c=(i.then?(await i)():i)[0],r()}catch(e){r(e)}})},38958:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\woocommerce-test\success\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1056,8702],()=>t(85051));module.exports=r})();