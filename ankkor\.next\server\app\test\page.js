(()=>{var e={};e.id=7928,e.ids=[7928],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},58188:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>c.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>g,tree:()=>o}),s(65413),s(52617),s(12523);var t=s(23191),a=s(88716),i=s(37922),c=s.n(i),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(r,l);let o=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65413)),"E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"],m="/test/page",u={require:s,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},68302:(e,r,s)=>{Promise.resolve().then(s.bind(s,75072))},75072:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{default:()=>g});var a=s(10326),i=s(17577),c=s(91664),n=s(15725),l=s(54337),o=s(32913),d=s(75290),m=e([n,l,o]);[n,l,o]=m.then?(await m)():m;let u=({title:e,children:r})=>(0,a.jsxs)("div",{className:"mb-8 border rounded-md p-4",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:e}),r]}),g=()=>{let[e,r]=(0,i.useState)([]),[s,t]=(0,i.useState)([]),[m,g]=(0,i.useState)(null),[p,h]=(0,i.useState)(null),[x,j]=(0,i.useState)({}),[N,y]=(0,i.useState)({}),b=(0,l.xS)(),v=(e,r)=>{j(s=>({...s,[e]:r}))},f=(e,r)=>{y(s=>({...s,[e]:r}))},w=async()=>{try{v("products",!0);let e=await (0,n.Xp)();r(e.nodes||[]),f("products",`Success! Fetched ${e.nodes?.length||0} products`)}catch(e){console.error("Error fetching products:",e),f("products",`Error: ${e.message}`)}finally{v("products",!1)}},E=async()=>{try{v("categories",!0);let e=await (0,n.CP)();t(e.nodes||[]),f("categories",`Success! Fetched ${e.nodes?.length||0} categories`)}catch(e){console.error("Error fetching categories:",e),f("categories",`Error: ${e.message}`)}finally{v("categories",!1)}},k=async()=>{if(!e.length){f("product","Error: No products available to test with");return}try{v("product",!0);let r=e[0].databaseId,s=await (0,n.wv)(r);g(s),f("product",`Success! Fetched product: ${s.name}`)}catch(e){console.error("Error fetching product:",e),f("product",`Error: ${e.message}`)}finally{v("product",!1)}},$=async()=>{if(!e.length){f("cart","Error: No products available to test with");return}try{v("cart",!0);let r=e[0];await b.addToCart({productId:r.databaseId.toString(),name:r.name,price:r.price,quantity:1,image:{url:r.image?.sourceUrl||"",altText:r.image?.altText||r.name}}),f("cart",`Success! Added ${r.name} to cart`)}catch(e){console.error("Error adding to cart:",e),f("cart",`Error: ${e.message}`)}finally{v("cart",!1)}},C=async()=>{try{v("login",!0);let e=await (0,o.x4)("<EMAIL>","password123");e&&(h(e),f("login",`Success! Logged in as ${e.email}`))}catch(e){console.error("Error logging in:",e),f("login",`Error: ${e.message}`)}finally{v("login",!1)}},P=async()=>{try{v("register",!0);let e=`test${Math.floor(1e4*Math.random())}@example.com`;await (0,o.z2)({email:e,firstName:"Test",lastName:"User",password:"password123",username:`testuser${Math.floor(1e4*Math.random())}`}),f("register",`Success! Registered user: ${e}`)}catch(e){console.error("Error registering:",e),f("register",`Error: ${e.message}`)}finally{v("register",!1)}},S=async()=>{try{v("currentUser",!0);let e=await (0,o.ts)();e?(h(e),f("currentUser",`Success! Current user: ${e.email}`)):f("currentUser","No user is currently logged in")}catch(e){console.error("Error getting current user:",e),f("currentUser",`Error: ${e.message}`)}finally{v("currentUser",!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Integration Test"}),a.jsx(u,{title:"Products",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(c.z,{onClick:w,disabled:x.products,children:[x.products&&a.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Products"]}),N.products&&a.jsx("div",{className:`p-3 rounded-md ${N.products.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:N.products}),e.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"First 5 Products:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:e.slice(0,5).map(e=>(0,a.jsxs)("li",{children:[e.name," - $",e.price]},e.id))})]})]})}),a.jsx(u,{title:"Categories",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(c.z,{onClick:E,disabled:x.categories,children:[x.categories&&a.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Categories"]}),N.categories&&a.jsx("div",{className:`p-3 rounded-md ${N.categories.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:N.categories}),s.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Categories:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:s.map(e=>a.jsx("li",{children:e.name},e.id))})]})]})}),a.jsx(u,{title:"Single Product",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(c.z,{onClick:k,disabled:x.product||!e.length,children:[x.product&&a.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Single Product"]}),N.product&&a.jsx("div",{className:`p-3 rounded-md ${N.product.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:N.product}),m&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium text-lg",children:m.name}),(0,a.jsxs)("p",{className:"text-gray-500 mt-1",children:["$",m.price]}),m.image&&a.jsx("div",{className:"mt-2 w-32 h-32 relative",children:a.jsx("img",{src:m.image.sourceUrl,alt:m.image.altText||m.name,className:"object-cover w-full h-full"})})]})]})}),a.jsx(u,{title:"Cart",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(c.z,{onClick:$,disabled:x.cart||!e.length,children:[x.cart&&a.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add to Cart"]}),N.cart&&a.jsx("div",{className:`p-3 rounded-md ${N.cart.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:N.cart}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"font-medium mb-2",children:["Cart Items: ",b.items.length]}),b.items.length>0&&a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:b.items.map(e=>(0,a.jsxs)("li",{children:[e.name," - Qty: ",e.quantity]},e.id))})]})]})}),a.jsx(u,{title:"Authentication",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(c.z,{onClick:C,disabled:x.login,children:[x.login&&a.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Login"]}),(0,a.jsxs)(c.z,{onClick:P,disabled:x.register,variant:"outline",children:[x.register&&a.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Register"]}),(0,a.jsxs)(c.z,{onClick:S,disabled:x.currentUser,variant:"secondary",children:[x.currentUser&&a.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Get Current User"]})]}),N.login&&a.jsx("div",{className:`p-3 rounded-md ${N.login.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:N.login}),N.register&&a.jsx("div",{className:`p-3 rounded-md ${N.register.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:N.register}),N.currentUser&&a.jsx("div",{className:`p-3 rounded-md ${N.currentUser.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:N.currentUser}),p&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium",children:"Current User:"}),(0,a.jsxs)("p",{children:["Email: ",p.email]}),(0,a.jsxs)("p",{children:["Name: ",p.firstName," ",p.lastName]})]})]})})]})};t()}catch(e){t(e)}})},32913:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{ts:()=>a.ts,x4:()=>a.x4,z2:()=>a.z2});var a=s(61296),i=e([a]);a=(i.then?(await i)():i)[0],t()}catch(e){t(e)}})},65413:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(19510);let a=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\test\WooCommerceTest.tsx#default`);function i(){return t.jsx(a,{})}}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,1056,8702],()=>s(58188));module.exports=t})();