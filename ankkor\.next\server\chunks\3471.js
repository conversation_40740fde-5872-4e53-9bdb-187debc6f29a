"use strict";exports.id=3471,exports.ids=[3471],exports.modules={53471:(e,t,a)=>{a.a(e,async(e,s)=>{try{a.d(t,{Z:()=>v});var r=a(10326),i=a(17577),o=a(90434),n=a(92148),l=a(67427),c=a(75290),d=a(34565),p=a(86806),u=a(96040),m=a(68897),f=a(77321),g=a(44960),x=a(68471),h=a(40381),y=e([u,m,f]);[u,m,f]=y.then?(await y)():y;let b=e=>{if("number"==typeof e)return e.toString();if(!e)return"0";let t=e.toString().replace(/[^\d.-]/g,""),a=parseFloat(t);return isNaN(a)?"0":a.toString()},v=({id:e,name:t,price:a,image:s,slug:y,material:v,isNew:w=!1,stockStatus:j="IN_STOCK",compareAtPrice:N=null,regularPrice:$=null,salePrice:S=null,onSale:k=!1,currencySymbol:E=x.J6,currencyCode:O=x.EJ,shortDescription:A,type:C})=>{let[I,D]=(0,i.useState)(!1),T=(0,p.rY)(),{openCart:F}=(0,f.j)(),{addToWishlist:z,isInWishlist:_,removeFromWishlist:P}=(0,u.Y)(),{isAuthenticated:H}=(0,m.O)(),M=_(e),Z=async r=>{if(r.preventDefault(),r.stopPropagation(),!e||""===e){console.error("Cannot add to cart: Missing product ID for product",t),h.Am.error("Cannot add to cart: Invalid product");return}if(!I){D(!0),console.log(`Adding product to cart: ${t} (ID: ${e})`);try{await T.addToCart({productId:e,quantity:1,name:t,price:a,image:{url:s,altText:t}}),h.Am.success(`${t} added to cart!`),F()}catch(e){console.error(`Failed to add ${t} to cart:`,e),h.Am.error("Failed to add item to cart. Please try again.")}finally{D(!1)}}},L=r=>{r.preventDefault(),r.stopPropagation(),M?(P(e),h.Am.success("Removed from wishlist")):(z({id:e,name:t,price:b(a),image:s,handle:y,material:v||"Material not specified",variantId:e}),H?h.Am.success("Added to your wishlist"):h.Am.success("Added to wishlist (saved locally)"))},R=N&&parseFloat(N)>parseFloat(a)?Math.round((parseFloat(N)-parseFloat(a))/parseFloat(N)*100):null,K="IN_STOCK"!==j;return(0,r.jsxs)(n.E.div,{className:"group relative",whileHover:{y:-5},transition:{duration:.3},children:[(0,r.jsxs)(o.default,{href:`/product/${y}`,className:"block",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden mb-4",children:[r.jsx("div",{className:"aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden",children:r.jsx(g.Z,{src:s,alt:t,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",animate:!0,className:"h-full"})}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[r.jsx(n.E.button,{onClick:L,className:`p-2 rounded-none ${M?"bg-[#2c2c27]":"bg-[#f8f8f5]"}`,whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":M?"Remove from wishlist":"Add to wishlist",children:r.jsx(l.Z,{className:`h-5 w-5 ${M?"text-[#f4f3f0] fill-current":"text-[#2c2c27]"}`})}),r.jsx(n.E.button,{onClick:Z,className:`p-2 rounded-none ${K||I?"bg-gray-400 cursor-not-allowed":"bg-[#2c2c27]"} text-[#f4f3f0]`,whileHover:K||I?{}:{scale:1.05},whileTap:K||I?{}:{scale:.95},"aria-label":K?"Out of stock":I?"Adding to cart...":"Add to cart",disabled:K||I,children:I?r.jsx(c.Z,{className:"h-5 w-5 animate-spin"}):r.jsx(d.Z,{className:"h-5 w-5"})})]}),w&&r.jsx("div",{className:"absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"New"}),K&&r.jsx("div",{className:"absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"Out of Stock"}),!K&&R&&(0,r.jsxs)("div",{className:"absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:[R,"% Off"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h3",{className:"font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2",children:t}),v&&r.jsx("p",{className:"text-[#8a8778] text-xs",children:v}),C&&r.jsx("p",{className:"text-[#8a8778] text-xs capitalize",children:C.toLowerCase().replace("_"," ")}),A&&r.jsx("p",{className:"text-[#5c5c52] text-xs line-clamp-2",dangerouslySetInnerHTML:{__html:A.replace(/<[^>]*>/g,"")}}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 product-card-price",children:[r.jsx("p",{className:"text-[#2c2c27] font-medium",children:k&&S?S.toString().includes("₹")||S.toString().includes("$")||S.toString().includes("€")||S.toString().includes("\xa3")?S:`${E}${S}`:a.toString().includes("₹")||a.toString().includes("$")||a.toString().includes("€")||a.toString().includes("\xa3")?a:`${E}${a}`}),k&&$&&r.jsx("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:$.toString().includes("₹")||$.toString().includes("$")||$.toString().includes("€")||$.toString().includes("\xa3")?$:`${E}${$}`}),!k&&N&&parseFloat(N.toString().replace(/[₹$€£]/g,""))>parseFloat(a.toString().replace(/[₹$€£]/g,""))&&r.jsx("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:N.toString().includes("₹")||N.toString().includes("$")||N.toString().includes("€")||N.toString().includes("\xa3")?N:`${E}${N}`})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center gap-2",children:"IN_STOCK"===j?r.jsx("span",{className:"text-green-600 text-xs font-medium",children:"✓ In Stock"}):"OUT_OF_STOCK"===j?r.jsx("span",{className:"text-red-600 text-xs font-medium",children:"✗ Out of Stock"}):"ON_BACKORDER"===j?r.jsx("span",{className:"text-orange-600 text-xs font-medium",children:"⏳ Backorder"}):r.jsx("span",{className:"text-gray-600 text-xs font-medium",children:"? Unknown"})}),k&&r.jsx("span",{className:"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium",children:"Sale"})]})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 space-y-2",children:[r.jsx(n.E.button,{onClick:Z,className:`w-full py-3 px-4 transition-all duration-200 ${K||I?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#1a1a17]"}`,whileHover:K||I?{}:{scale:1.02},whileTap:K||I?{}:{scale:.98},"aria-label":K?"Out of stock":I?"Adding to cart...":"Add to cart",disabled:K||I,children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[I?r.jsx(c.Z,{className:"h-4 w-4 animate-spin"}):r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"text-sm font-medium",children:K?"Out of Stock":I?"Adding...":"Add to Cart"})]})}),r.jsx(n.E.button,{onClick:L,className:`w-full py-3 px-4 border transition-all duration-200 ${M?"bg-[#2c2c27] text-[#f4f3f0] border-[#2c2c27]":"bg-transparent text-[#2c2c27] border-[#2c2c27] hover:bg-[#2c2c27] hover:text-[#f4f3f0]"}`,whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":M?"Remove from wishlist":"Add to wishlist",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[r.jsx(l.Z,{className:`h-4 w-4 ${M?"fill-current":""}`}),r.jsx("span",{className:"text-sm font-medium",children:M?"In Wishlist":"Add to Wishlist"})]})})]})]})};s()}catch(e){s(e)}})},44960:(e,t,a)=>{a.d(t,{Z:()=>n});var s=a(10326),r=a(17577),i=a(46226),o=a(92148);let n=({src:e,alt:t,width:a,height:n,fill:l=!1,sizes:c=l?"(max-width: 768px) 100vw, 50vw":void 0,priority:d=!1,className:p="",animate:u=!0,style:m={}})=>{let[f,g]=(0,r.useState)(!0),[x,h]=(0,r.useState)(!1);return(0,s.jsxs)("div",{className:`relative overflow-hidden ${p}`,style:{minHeight:l?"100%":void 0,height:l?"100%":void 0,...m},onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:[f&&s.jsx(o.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),s.jsx(o.E.div,{className:"w-full h-full",animate:u&&x?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:s.jsx(i.default,{src:e,alt:t,width:a,height:n,fill:l,sizes:c,priority:d,className:`
            ${f?"opacity-0":"opacity-100"} 
            transition-opacity duration-500
            ${l?"object-cover":""}
          `,onLoad:()=>g(!1)})})]})}},40381:(e,t,a)=>{a.d(t,{x7:()=>ec,Am:()=>T});var s,r=a(17577);let i={data:""},o=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||i,n=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,l=/\/\*[^]*?\*\/|  +/g,c=/\n+/g,d=(e,t)=>{let a="",s="",r="";for(let i in e){let o=e[i];"@"==i[0]?"i"==i[1]?a=i+" "+o+";":s+="f"==i[1]?d(o,i):i+"{"+d(o,"k"==i[1]?"":t)+"}":"object"==typeof o?s+=d(o,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=o&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=d.p?d.p(i,o):i+":"+o+";")}return a+(t&&r?t+"{"+r+"}":r)+s},p={},u=e=>{if("object"==typeof e){let t="";for(let a in e)t+=a+u(e[a]);return t}return e},m=(e,t,a,s,r)=>{let i=u(e),o=p[i]||(p[i]=(e=>{let t=0,a=11;for(;t<e.length;)a=101*a+e.charCodeAt(t++)>>>0;return"go"+a})(i));if(!p[o]){let t=i!==e?e:(e=>{let t,a,s=[{}];for(;t=n.exec(e.replace(l,""));)t[4]?s.shift():t[3]?(a=t[3].replace(c," ").trim(),s.unshift(s[0][a]=s[0][a]||{})):s[0][t[1]]=t[2].replace(c," ").trim();return s[0]})(e);p[o]=d(r?{["@keyframes "+o]:t}:t,a?"":"."+o)}let m=a&&p.g?p.g:null;return a&&(p.g=p[o]),((e,t,a,s)=>{s?t.data=t.data.replace(s,e):-1===t.data.indexOf(e)&&(t.data=a?e+t.data:t.data+e)})(p[o],t,s,m),o},f=(e,t,a)=>e.reduce((e,s,r)=>{let i=t[r];if(i&&i.call){let e=i(a),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":d(e,""):!1===e?"":e}return e+s+(null==i?"":i)},"");function g(e){let t=this||{},a=e.call?e(t.p):e;return m(a.unshift?a.raw?f(a,[].slice.call(arguments,1),t.p):a.reduce((e,a)=>Object.assign(e,a&&a.call?a(t.p):a),{}):a,o(t.target),t.g,t.o,t.k)}g.bind({g:1});let x,h,y,b=g.bind({k:1});function v(e,t){let a=this||{};return function(){let s=arguments;function r(i,o){let n=Object.assign({},i),l=n.className||r.className;a.p=Object.assign({theme:h&&h()},n),a.o=/ *go\d+/.test(l),n.className=g.apply(a,s)+(l?" "+l:""),t&&(n.ref=o);let c=e;return e[0]&&(c=n.as||e,delete n.as),y&&c[0]&&y(n),x(c,n)}return t?t(r):r}}var w=e=>"function"==typeof e,j=(e,t)=>w(e)?e(t):e,N=(()=>{let e=0;return()=>(++e).toString()})(),$=(()=>{let e;return()=>e})(),S=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:a}=t;return S(e,{type:e.toasts.find(e=>e.id===a.id)?1:0,toast:a});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+r}))}}},k=[],E={toasts:[],pausedAt:void 0},O=e=>{E=S(E,e),k.forEach(e=>{e(E)})},A={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},C=(e={})=>{let[t,a]=(0,r.useState)(E),s=(0,r.useRef)(E);(0,r.useEffect)(()=>(s.current!==E&&a(E),k.push(a),()=>{let e=k.indexOf(a);e>-1&&k.splice(e,1)}),[]);let i=t.toasts.map(t=>{var a,s,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||A[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}});return{...t,toasts:i}},I=(e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||N()}),D=e=>(t,a)=>{let s=I(t,e,a);return O({type:2,toast:s}),s.id},T=(e,t)=>D("blank")(e,t);T.error=D("error"),T.success=D("success"),T.loading=D("loading"),T.custom=D("custom"),T.dismiss=e=>{O({type:3,toastId:e})},T.remove=e=>O({type:4,toastId:e}),T.promise=(e,t,a)=>{let s=T.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let r=t.success?j(t.success,e):void 0;return r?T.success(r,{id:s,...a,...null==a?void 0:a.success}):T.dismiss(s),e}).catch(e=>{let r=t.error?j(t.error,e):void 0;r?T.error(r,{id:s,...a,...null==a?void 0:a.error}):T.dismiss(s)}),e};var F=(e,t)=>{O({type:1,toast:{id:e,height:t}})},z=()=>{O({type:5,time:Date.now()})},_=new Map,P=1e3,H=(e,t=P)=>{if(_.has(e))return;let a=setTimeout(()=>{_.delete(e),O({type:4,toastId:e})},t);_.set(e,a)},M=e=>{let{toasts:t,pausedAt:a}=C(e);(0,r.useEffect)(()=>{if(a)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let a=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(a<0){t.visible&&T.dismiss(t.id);return}return setTimeout(()=>T.dismiss(t.id),a)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,a]);let s=(0,r.useCallback)(()=>{a&&O({type:6,time:Date.now()})},[a]),i=(0,r.useCallback)((e,a)=>{let{reverseOrder:s=!1,gutter:r=8,defaultPosition:i}=a||{},o=t.filter(t=>(t.position||i)===(e.position||i)&&t.height),n=o.findIndex(t=>t.id===e.id),l=o.filter((e,t)=>t<n&&e.visible).length;return o.filter(e=>e.visible).slice(...s?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+r,0)},[t]);return(0,r.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)H(e.id,e.removeDelay);else{let t=_.get(e.id);t&&(clearTimeout(t),_.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:F,startPause:z,endPause:s,calculateOffset:i}}},Z=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,L=b`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,R=b`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,K=v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Z} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${L} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${R} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,U=b`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,B=v("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${U} 1s linear infinite;
`,Y=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,q=b`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,J=v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Y} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${q} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,W=v("div")`
  position: absolute;
`,G=v("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Q=b`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,V=v("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Q} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,X=({toast:e})=>{let{icon:t,type:a,iconTheme:s}=e;return void 0!==t?"string"==typeof t?r.createElement(V,null,t):t:"blank"===a?null:r.createElement(G,null,r.createElement(B,{...s}),"loading"!==a&&r.createElement(W,null,"error"===a?r.createElement(K,{...s}):r.createElement(J,{...s})))},ee=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,et=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=v("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,es=v("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,er=(e,t)=>{let a=e.includes("top")?1:-1,[s,r]=$()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[ee(a),et(a)];return{animation:t?`${b(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${b(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},ei=r.memo(({toast:e,position:t,style:a,children:s})=>{let i=e.height?er(e.position||t||"top-center",e.visible):{opacity:0},o=r.createElement(X,{toast:e}),n=r.createElement(es,{...e.ariaProps},j(e.message,e));return r.createElement(ea,{className:e.className,style:{...i,...a,...e.style}},"function"==typeof s?s({icon:o,message:n}):r.createElement(r.Fragment,null,o,n))});s=r.createElement,d.p=void 0,x=s,h=void 0,y=void 0;var eo=({id:e,className:t,style:a,onHeightUpdate:s,children:i})=>{let o=r.useCallback(t=>{if(t){let a=()=>{s(e,t.getBoundingClientRect().height)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return r.createElement("div",{ref:o,className:t,style:a},i)},en=(e,t)=>{let a=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:$()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...a?{top:0}:{bottom:0},...s}},el=g`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ec=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:s,children:i,containerStyle:o,containerClassName:n})=>{let{toasts:l,handlers:c}=M(a);return r.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:n,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(a=>{let o=a.position||t,n=en(o,c.calculateOffset(a,{reverseOrder:e,gutter:s,defaultPosition:t}));return r.createElement(eo,{id:a.id,key:a.id,onHeightUpdate:c.updateHeight,className:a.visible?el:"",style:n},"custom"===a.type?j(a.message,a):i?i(a):r.createElement(ei,{toast:a,position:o}))}))}}};