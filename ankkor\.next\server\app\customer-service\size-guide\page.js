(()=>{var e={};e.id=9288,e.ids=[9288],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},39047:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>c.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>l}),t(82043),t(52617),t(12523);var r=t(23191),a=t(88716),i=t(37922),c=t.n(i),o=t(95231),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let l=["",{children:["customer-service",{children:["size-guide",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,82043)),"E:\\ankkorwoo\\ankkor\\src\\app\\customer-service\\size-guide\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\customer-service\\size-guide\\page.tsx"],m="/customer-service/size-guide/page",h={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/customer-service/size-guide/page",pathname:"/customer-service/size-guide",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},39164:(e,s,t)=>{Promise.resolve().then(t.bind(t,86025))},6035:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});let r=(0,t(76557).Z)("Ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]])},86025:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(10326),a=t(17577),i=t(90434),c=t(46226),o=t(6035),n=t(18019);let l=({title:e,headers:s,rows:t,units:a})=>(0,r.jsxs)("div",{className:"mb-12",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h3",{className:"text-xl font-serif font-bold text-[#2c2c27]",children:e}),(0,r.jsxs)("span",{className:"text-sm text-[#8a8778]",children:["Measurements in ",a]})]}),r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full border-collapse",children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{className:"bg-[#f4f3f0]",children:[r.jsx("th",{className:"border border-[#e5e2d9] py-3 px-4 text-left text-[#2c2c27] font-medium",children:"Size"}),s.map((e,s)=>r.jsx("th",{className:"border border-[#e5e2d9] py-3 px-4 text-left text-[#2c2c27] font-medium",children:e},s))]})}),r.jsx("tbody",{children:t.map((e,s)=>(0,r.jsxs)("tr",{className:s%2==0?"bg-white":"bg-[#faf9f6]",children:[r.jsx("td",{className:"border border-[#e5e2d9] py-3 px-4 font-medium text-[#2c2c27]",children:e.size}),e.measurements.map((e,s)=>r.jsx("td",{className:"border border-[#e5e2d9] py-3 px-4 text-[#5c5c52]",children:e},s))]},s))})]})})]}),d=({title:e,description:s,image:t})=>(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 mb-8 items-center",children:[r.jsx("div",{className:"md:w-1/3 relative h-[200px] w-full",children:r.jsx(c.default,{src:t,alt:e,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"})}),(0,r.jsxs)("div",{className:"md:w-2/3",children:[r.jsx("h4",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:e}),r.jsx("p",{className:"text-[#5c5c52]",children:s})]})]});function m(){let[e,s]=(0,a.useState)("inches"),t={headers:["Chest","Waist","Sleeve Length","Shoulder Width","Neck"],rows:[{size:"XS",measurements:["34-36","28-30","32","17","14.5"]},{size:"S",measurements:["36-38","30-32","33","17.5","15"]},{size:"M",measurements:["38-40","32-34","34","18","15.5"]},{size:"L",measurements:["40-42","34-36","35","18.5","16"]},{size:"XL",measurements:["42-44","36-38","36","19","16.5"]},{size:"XXL",measurements:["44-46","38-40","37","19.5","17"]}]},c={headers:["Chest","Waist","Sleeve Length","Shoulder Width","Neck"],rows:[{size:"XS",measurements:["86-91","71-76","81","43","37"]},{size:"S",measurements:["91-97","76-81","84","44","38"]},{size:"M",measurements:["97-102","81-86","86","46","39"]},{size:"L",measurements:["102-107","86-91","89","47","41"]},{size:"XL",measurements:["107-112","91-97","91","48","42"]},{size:"XXL",measurements:["112-117","97-102","94","50","43"]}]};return r.jsx("div",{className:"min-h-screen bg-[#f8f8f5] py-12",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"mb-8 text-sm text-[#8a8778]",children:[r.jsx(i.default,{href:"/",className:"hover:text-[#2c2c27] transition-colors",children:"Home"}),r.jsx("span",{className:"mx-2",children:"/"}),r.jsx(i.default,{href:"/customer-service",className:"hover:text-[#2c2c27] transition-colors",children:"Customer Service"}),r.jsx("span",{className:"mx-2",children:"/"}),r.jsx("span",{className:"text-[#2c2c27]",children:"Size Guide"})]}),(0,r.jsxs)("div",{className:"text-center max-w-3xl mx-auto mb-16",children:[r.jsx("h1",{className:"text-4xl font-serif font-bold mb-6 text-[#2c2c27]",children:"Size Guide"}),r.jsx("p",{className:"text-[#5c5c52] leading-relaxed",children:"Find your perfect fit with our comprehensive size charts. If you're between sizes, we recommend sizing up for a more comfortable fit or contacting our customer service team for personalized assistance."})]}),r.jsx("div",{className:"flex justify-center mb-12",children:(0,r.jsxs)("div",{className:"inline-flex border border-[#e5e2d9] rounded-none overflow-hidden",children:[r.jsx("button",{onClick:()=>s("inches"),className:`px-6 py-2 text-sm ${"inches"===e?"bg-[#2c2c27] text-[#f4f3f0]":"bg-[#f4f3f0] text-[#2c2c27] hover:bg-[#e5e2d9]"} transition-colors`,children:"Inches"}),r.jsx("button",{onClick:()=>s("cm"),className:`px-6 py-2 text-sm ${"cm"===e?"bg-[#2c2c27] text-[#f4f3f0]":"bg-[#f4f3f0] text-[#2c2c27] hover:bg-[#e5e2d9]"} transition-colors`,children:"Centimeters"})]})}),r.jsx("div",{className:"mb-16",children:r.jsx(l,{title:"Shirts",headers:"inches"===e?t.headers:c.headers,rows:"inches"===e?t.rows:c.rows,units:e})}),(0,r.jsxs)("div",{className:"bg-[#f4f3f0] p-8 border border-[#e5e2d9] mb-16",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[r.jsx(o.Z,{className:"h-5 w-5 text-[#8a8778]"}),r.jsx("h2",{className:"text-2xl font-serif font-bold text-[#2c2c27]",children:"How to Measure"})]}),r.jsx("p",{className:"text-[#5c5c52]",children:"For the most accurate measurements, we recommend having someone else measure you. Wear lightweight clothing and stand straight with your feet together."})]}),r.jsx("div",{className:"space-y-10",children:[{title:"Chest",description:"Measure around the fullest part of your chest, keeping the tape measure horizontal and under your arms.",image:"https://images.unsplash.com/photo-1594938298603-c8148c4dae35?q=80"},{title:"Waist",description:"Measure around your natural waistline, which is located above your hip bones and below your ribcage. Keep the tape measure snug but not tight.",image:"https://images.unsplash.com/photo-1598032895397-b9472444bf93?q=80"},{title:"Sleeve Length",description:"Measure from the center back of your neck, across your shoulder, and down to your wrist. Keep your arm slightly bent.",image:"https://images.unsplash.com/photo-1594938298613-c9546b6f6c51?q=80"},{title:"Inseam",description:"Measure from the crotch seam to the bottom of the leg. For the most accurate measurement, use a pair of pants that fit you well.",image:"https://images.unsplash.com/photo-1584865288642-42078afe6942?q=80"}].map((e,s)=>r.jsx(d,{title:e.title,description:e.description,image:e.image},s))})]}),(0,r.jsxs)("div",{className:"bg-white p-8 border border-[#e5e2d9] mb-16",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3 mb-6",children:[r.jsx("div",{className:"text-[#8a8778] mt-1",children:r.jsx(n.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Fit Notes"}),r.jsx("p",{className:"text-[#5c5c52] mb-4",children:"At Ankkor, we offer the following fits across our collection:"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:"Classic Fit"}),r.jsx("p",{className:"text-[#5c5c52]",children:"Our most generous fit, designed for comfort with a relaxed silhouette. Classic fit shirts have a fuller cut through the chest and waist."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:"Tailored Fit"}),r.jsx("p",{className:"text-[#5c5c52]",children:"A refined silhouette that's trimmer than our Classic fit but not overly slim. Tailored fit offers a clean, modern profile without being restrictive."})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:"Slim Fit"}),r.jsx("p",{className:"text-[#5c5c52]",children:"Our most fitted silhouette, cut close to the body for a contemporary look. Slim fit shirts are narrower through the chest and waist with higher armholes."})]})]})]}),(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h2",{className:"text-2xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Need Additional Assistance?"}),r.jsx("p",{className:"text-[#5c5c52] mb-6 max-w-2xl mx-auto",children:"If you have any questions about sizing or need personalized recommendations, our customer service team is here to help."}),r.jsx(i.default,{href:"/customer-service/contact",className:"inline-block bg-[#2c2c27] text-[#f4f3f0] px-8 py-3 hover:bg-[#3d3d35] transition-colors text-sm tracking-wider uppercase font-medium",children:"Contact Us"})]})]})})}},82043:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\customer-service\size-guide\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1056,8702],()=>t(39047));module.exports=r})();