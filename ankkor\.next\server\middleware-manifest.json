{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "rc_xCTyhb1nU3epuEYG-R", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DRTXjt3jOxA8DlJtQr/LvwnChCAKRHtPTqJIJgXlHl4=", "__NEXT_PREVIEW_MODE_ID": "8b3973211d7a04d5a5fdeb8c394c04bf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "34359891730de814a5019e9095eebcd63c82bc480335ede0894c1e70fb93e6e5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3acd664085273bb1f5fa8954c61d2b6af195a77be186d3ba22e2a5a72ad1fd75"}}}, "functions": {}, "sortedMiddleware": ["/"]}