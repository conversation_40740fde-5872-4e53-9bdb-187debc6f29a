{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "W-_PG8hHzNSSCRZULKJzM", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "w+ocZgB28XM01VNh+wdS8LaHN9vWd2cjFlhlyP/lw4w=", "__NEXT_PREVIEW_MODE_ID": "57084acfce30d49220dfd4aa2586247e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "777d09fecf103f485094b6df99f7e5585fb1ef8c0806513b9972ddba12ec394c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "51fee64d1ddbd225aad2a5b78c348a47fcca73c9c98f26c9d35e7c6dba4dfb43"}}}, "functions": {}, "sortedMiddleware": ["/"]}