"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6758],{62670:function(e,t,a){var s=a(57437),r=a(2265),n=a(27648),i=a(43886),o=a(88997),c=a(15863),l=a(42449),d=a(87758),u=a(92371),m=a(3371),f=a(16194),h=a(57152),x=a(70597),g=a(11738);let p=e=>{if("number"==typeof e)return e.toString();if(!e)return"0";let t=parseFloat(e.toString().replace(/[^\d.-]/g,""));return isNaN(t)?"0":t.toString()};t.Z=e=>{let{id:t,name:a,price:v,image:N,slug:b,material:y,isNew:j=!1,stockStatus:w="IN_STOCK",compareAtPrice:k=null,regularPrice:S=null,salePrice:C=null,onSale:E=!1,currencySymbol:I=x.J6,currencyCode:A=x.EJ,shortDescription:P,type:O}=e,[L,T]=(0,r.useState)(!1),_=(0,d.rY)(),{openCart:z}=(0,f.j)(),{addToWishlist:F,isInWishlist:H,removeFromWishlist:R}=(0,u.Y)(),{isAuthenticated:U}=(0,m.O)(),Z=H(t),M=async e=>{if(e.preventDefault(),e.stopPropagation(),!t||""===t){console.error("Cannot add to cart: Missing product ID for product",a),g.Am.error("Cannot add to cart: Invalid product");return}if(!L){T(!0),console.log("Adding product to cart: ".concat(a," (ID: ").concat(t,")"));try{await _.addToCart({productId:t,quantity:1,name:a,price:v,image:{url:N,altText:a}}),g.Am.success("".concat(a," added to cart!")),z()}catch(e){console.error("Failed to add ".concat(a," to cart:"),e),g.Am.error("Failed to add item to cart. Please try again.")}finally{T(!1)}}},Y=e=>{e.preventDefault(),e.stopPropagation(),Z?(R(t),g.Am.success("Removed from wishlist")):(F({id:t,name:a,price:p(v),image:N,handle:b,material:y||"Material not specified",variantId:t}),U?g.Am.success("Added to your wishlist"):g.Am.success("Added to wishlist (saved locally)"))},$=k&&parseFloat(k)>parseFloat(v)?Math.round((parseFloat(k)-parseFloat(v))/parseFloat(k)*100):null,B="IN_STOCK"!==w;return(0,s.jsxs)(i.E.div,{className:"group relative",whileHover:{y:-5},transition:{duration:.3},children:[(0,s.jsxs)(n.default,{href:"/product/".concat(b),className:"block",children:[(0,s.jsxs)("div",{className:"relative overflow-hidden mb-4",children:[(0,s.jsx)("div",{className:"aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden",children:(0,s.jsx)(h.Z,{src:N,alt:a,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",animate:!0,className:"h-full"})}),(0,s.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,s.jsx)(i.E.button,{onClick:Y,className:"p-2 rounded-none ".concat(Z?"bg-[#2c2c27]":"bg-[#f8f8f5]"),whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":Z?"Remove from wishlist":"Add to wishlist",children:(0,s.jsx)(o.Z,{className:"h-5 w-5 ".concat(Z?"text-[#f4f3f0] fill-current":"text-[#2c2c27]")})}),(0,s.jsx)(i.E.button,{onClick:M,className:"p-2 rounded-none ".concat(B||L?"bg-gray-400 cursor-not-allowed":"bg-[#2c2c27]"," text-[#f4f3f0]"),whileHover:B||L?{}:{scale:1.05},whileTap:B||L?{}:{scale:.95},"aria-label":B?"Out of stock":L?"Adding to cart...":"Add to cart",disabled:B||L,children:L?(0,s.jsx)(c.Z,{className:"h-5 w-5 animate-spin"}):(0,s.jsx)(l.Z,{className:"h-5 w-5"})})]}),j&&(0,s.jsx)("div",{className:"absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"New"}),B&&(0,s.jsx)("div",{className:"absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"Out of Stock"}),!B&&$&&(0,s.jsxs)("div",{className:"absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:[$,"% Off"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2",children:a}),y&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs",children:y}),O&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs capitalize",children:O.toLowerCase().replace("_"," ")}),P&&(0,s.jsx)("p",{className:"text-[#5c5c52] text-xs line-clamp-2",dangerouslySetInnerHTML:{__html:P.replace(/<[^>]*>/g,"")}}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 product-card-price",children:[(0,s.jsx)("p",{className:"text-[#2c2c27] font-medium",children:E&&C?C.toString().includes("₹")||C.toString().includes("$")||C.toString().includes("€")||C.toString().includes("\xa3")?C:"".concat(I).concat(C):v.toString().includes("₹")||v.toString().includes("$")||v.toString().includes("€")||v.toString().includes("\xa3")?v:"".concat(I).concat(v)}),E&&S&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:S.toString().includes("₹")||S.toString().includes("$")||S.toString().includes("€")||S.toString().includes("\xa3")?S:"".concat(I).concat(S)}),!E&&k&&parseFloat(k.toString().replace(/[₹$€£]/g,""))>parseFloat(v.toString().replace(/[₹$€£]/g,""))&&(0,s.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:k.toString().includes("₹")||k.toString().includes("$")||k.toString().includes("€")||k.toString().includes("\xa3")?k:"".concat(I).concat(k)})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:"IN_STOCK"===w?(0,s.jsx)("span",{className:"text-green-600 text-xs font-medium",children:"✓ In Stock"}):"OUT_OF_STOCK"===w?(0,s.jsx)("span",{className:"text-red-600 text-xs font-medium",children:"✗ Out of Stock"}):"ON_BACKORDER"===w?(0,s.jsx)("span",{className:"text-orange-600 text-xs font-medium",children:"⏳ Backorder"}):(0,s.jsx)("span",{className:"text-gray-600 text-xs font-medium",children:"? Unknown"})}),E&&(0,s.jsx)("span",{className:"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium",children:"Sale"})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsx)(i.E.button,{onClick:M,className:"w-full py-3 px-4 transition-all duration-200 ".concat(B||L?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#1a1a17]"),whileHover:B||L?{}:{scale:1.02},whileTap:B||L?{}:{scale:.98},"aria-label":B?"Out of stock":L?"Adding to cart...":"Add to cart",disabled:B||L,children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[L?(0,s.jsx)(c.Z,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(l.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:B?"Out of Stock":L?"Adding...":"Add to Cart"})]})}),(0,s.jsx)(i.E.button,{onClick:Y,className:"w-full py-3 px-4 border transition-all duration-200 ".concat(Z?"bg-[#2c2c27] text-[#f4f3f0] border-[#2c2c27]":"bg-transparent text-[#2c2c27] border-[#2c2c27] hover:bg-[#2c2c27] hover:text-[#f4f3f0]"),whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":Z?"Remove from wishlist":"Add to wishlist",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(o.Z,{className:"h-4 w-4 ".concat(Z?"fill-current":"")}),(0,s.jsx)("span",{className:"text-sm font-medium",children:Z?"In Wishlist":"Add to Wishlist"})]})})]})]})}},3371:function(e,t,a){a.d(t,{CustomerProvider:function(){return f},O:function(){return m}});var s=a(57437),r=a(2265),n=a(99376),i=a(77690),o=a(68123),c=a(71917),l=a(67111),d=a(32898);let u=(0,r.createContext)({customer:null,isLoading:!0,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),m=()=>(0,r.useContext)(u);function f(e){let{children:t}=e,[a,m]=(0,r.useState)(null),[f,h]=(0,r.useState)(!0),[x,g]=(0,r.useState)(null),[p,v]=(0,r.useState)(null),N=(0,n.useRouter)(),{addToast:b}=(0,c.p)();(0,d.j)();let y=e=>e?{...e,displayName:e.displayName||e.username||"".concat(e.firstName||""," ").concat(e.lastName||"").trim()||"User"}:null,j=async()=>{try{console.log("CustomerProvider: Checking authentication via /api/auth/me");let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});console.log("CustomerProvider: Auth API response status:",e.status);let t=await e.json();if(console.log("CustomerProvider: Auth API result:",t),!t.success||!t.customer)return v(null),{success:!1,message:t.message||"Not authenticated"};{let e=t.token;return console.log("CustomerProvider: Token from API response:",!!e),v(e||null),{success:!0,customer:t.customer,token:e}}}catch(e){return console.error("CustomerProvider: Error checking authentication:",e),v(null),{success:!1,message:"Network error"}}},w=async()=>{try{let e=await j();if(e.success){let t={...e.customer,token:e.token};m(y(t)),console.log("Customer data refreshed successfully"),console.log("Token available after refresh:",!!e.token)}else console.log("Failed to refresh customer data:",e.message),m(null),v(null)}catch(e){console.error("Error refreshing customer data:",e),m(null),v(null)}};(0,r.useEffect)(()=>{(async()=>{try{h(!0);let e=await j();if(e.success){console.log("Found valid authentication, customer data loaded"),console.log("Token available on mount:",!!e.token);let t={...e.customer,token:e.token};m(y(t))}else console.log("No valid authentication found:",e.message),m(null),v(null)}catch(e){console.error("Error checking customer session:",e),(0,i.kS)(),m(null),v(null)}finally{h(!1)}})()},[]);let k=e=>{if(!e)return"An unknown error occurred";let t="string"==typeof e?e:e.message||JSON.stringify(e);return t.includes("Unidentified customer")?"The email or password you entered is incorrect. Please try again.":t.includes("already associated")?"An account with this email already exists. Please sign in instead.":t.includes("password")&&t.includes("too short")?"Your password must be at least 8 characters. Please try again.":t.includes("token")&&(t.includes("expired")||t.includes("invalid"))?"Your login session has expired. Please sign in again.":t.includes("network")||t.includes("failed to fetch")?"Network connection issue. Please check your internet connection and try again.":t},S=async e=>{h(!0),g(null);try{let t=await (0,i.x4)(e.email,e.password);if(!t||!t.success||!t.user)throw Error("Login failed: No user data returned");let a={id:t.user.id,databaseId:t.user.databaseId,email:t.user.email,firstName:t.user.firstName,lastName:t.user.lastName,token:t.token};m(y(a));let s=t.token;console.log("Login successful, token from API:",!!s),v(s||null);let r=l.xS.getState();try{await r.clearCart(),await r.initializeCart()}catch(e){console.error("Error initializing cart after login:",e)}b("Welcome back, ".concat((null==a?void 0:a.firstName)||"there","!"),"success"),N.push("/")}catch(t){let e=k(t);throw g(e),b(e,"error"),t}finally{h(!1)}},C=async e=>{h(!0),g(null);try{var t;let a=await (0,i.z2)(e.email,e.firstName,e.lastName,e.password);if(!a||!a.success||!a.customer)throw Error("Registration failed: No customer data returned");let s={...a.customer,token:a.token};m(y(s));let r=a.token;console.log("Registration successful, token from API:",!!r),v(r||null);let n=l.xS.getState();try{await n.clearCart(),await n.initializeCart()}catch(e){console.error("Error initializing cart after registration:",e)}b("Welcome to Ankkor, ".concat(null===(t=a.customer)||void 0===t?void 0:t.firstName,"!"),"success"),N.push("/")}catch(t){let e=k(t);throw g(e),b(e,"error"),t}finally{h(!1)}},E=async e=>{h(!0),g(null);try{let t=await (0,o.lG)(e);if(!t||!t.customer)throw Error("Profile update failed: No customer data returned");return m(y(t.customer)),b("Your profile has been updated successfully","success"),t}catch(t){let e=k(t);throw g(e),b(e,"error"),t}finally{h(!1)}};return(0,s.jsx)(u.Provider,{value:{customer:a,isLoading:f,isAuthenticated:!!a,token:p,login:S,register:C,logout:()=>{(0,i.kS)(),m(null),v(null),console.log("Logout completed, token cleared"),l.xS.getState().clearCart().catch(e=>{console.error("Error clearing cart during logout:",e)}),sessionStorage.removeItem("cartInitialized"),sessionStorage.removeItem("cartInitializationAttempts"),b("You have been signed out successfully","info"),N.push("/"),N.refresh()},updateProfile:E,error:x,refreshCustomer:w},children:t})}},64528:function(e,t,a){a.d(t,{Gd:function(){return c}});var s=a(57437),r=a(2265),n=a(59625),i=a(89134),o=a(40257);let c=(0,n.Ue)()((0,i.tJ)(e=>({isLaunchingSoon:"true"===o.env.NEXT_PUBLIC_LAUNCHING_SOON,setIsLaunchingSoon:e=>{console.warn("Changing launch state is disabled in production.")}}),{name:"ankkor-launch-state"})),l=(0,r.createContext)(void 0);t.default=e=>{let{children:t}=e,a=c(),[n,i]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{i(!0);{let e="true"===o.env.NEXT_PUBLIC_LAUNCHING_SOON;a.isLaunchingSoon!==e&&c.setState({isLaunchingSoon:e})}},[a]),(0,s.jsx)(l.Provider,{value:a,children:n?t:null})}},11658:function(e,t,a){a.d(t,{default:function(){return x},r:function(){return u}});var s=a(57437),r=a(2265),n=a(99376),i=a(48131),o=a(43886),c=e=>{let{size:t="md",variant:a="thread",className:r=""}=e,n={sm:{container:"w-16 h-16",text:"text-xs"},md:{container:"w-24 h-24",text:"text-sm"},lg:{container:"w-32 h-32",text:"text-base"}};return"thread"===a?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsxs)("div",{className:"relative ".concat(n[t].container),children:[(0,s.jsx)(o.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27",borderRightColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1.5,repeat:1/0,ease:"linear"}}),(0,s.jsx)(o.E.div,{className:"absolute inset-2 rounded-full border-2 border-[#e5e2d9]",style:{borderBottomColor:"#8a8778",borderLeftColor:"#8a8778"},animate:{rotate:-360},transition:{duration:2,repeat:1/0,ease:"linear"}}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-[#2c2c27]"})})]}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Loading Collection"})]}):"fabric"===a?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsxs)("div",{className:"relative ".concat(n[t].container," flex items-center justify-center"),children:[(0,s.jsx)(o.E.div,{className:"absolute w-1/3 h-1/3 bg-[#e5e2d9]",animate:{rotate:360,scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,s.jsx)(o.E.div,{className:"absolute w-1/3 h-1/3 bg-[#8a8778]",animate:{rotate:-360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.3}}),(0,s.jsx)(o.E.div,{className:"absolute w-1/3 h-1/3 bg-[#2c2c27]",animate:{rotate:360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.6}})]}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Preparing Your Style"})]}):"button"===a?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsx)("div",{className:"relative ".concat(n[t].container," flex items-center justify-center"),children:(0,s.jsx)("div",{className:"relative flex",children:[0,1,2,3].map(e=>(0,s.jsx)(o.E.div,{className:"w-3 h-3 mx-1 rounded-full bg-[#2c2c27] border border-[#8a8778]",animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,ease:"easeInOut",delay:.2*e}},e))})}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Tailoring Experience"})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(r),children:[(0,s.jsx)("div",{className:"relative ".concat(n[t].container),children:(0,s.jsx)(o.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),(0,s.jsx)("p",{className:"mt-4 font-serif text-[#5c5c52] ".concat(n[t].text),children:"Loading"})]})},l=e=>{let{isLoading:t,variant:a="thread"}=e;return(0,s.jsx)(i.M,{children:t&&(0,s.jsx)(o.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[200] flex items-center justify-center bg-[#f8f8f5]/90 backdrop-blur-sm",children:(0,s.jsx)(c,{variant:a,size:"lg"})})})};let d=(0,r.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),u=()=>(0,r.useContext)(d),m={"/collection":"fabric","/collection/shirts":"fabric","/collection/polos":"fabric","/product":"thread","/about":"button","/customer-service":"button","/account":"thread","/wishlist":"thread"},f=e=>{let{setIsLoading:t,setVariant:s}=e,i=(0,n.usePathname)(),{useSearchParams:o}=a(99376),c=o();return(0,r.useEffect)(()=>{t(!0),s(m["/"+i.split("/")[1]]||m[i]||"thread");let e=setTimeout(()=>{t(!1)},1200);return()=>clearTimeout(e)},[i,c,t,s]),null},h=()=>(0,s.jsx)("div",{className:"hidden",children:"Loading route..."});var x=e=>{let{children:t}=e,[a,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)("thread");return(0,s.jsxs)(d.Provider,{value:{isLoading:a,setLoading:n,variant:i,setVariant:o},children:[(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)(h,{}),children:(0,s.jsx)(f,{setIsLoading:n,setVariant:o})}),t,(0,s.jsx)(l,{isLoading:a,variant:i})]})}},57152:function(e,t,a){var s=a(57437),r=a(2265),n=a(33145),i=a(43886);t.Z=e=>{let{src:t,alt:a,width:o,height:c,fill:l=!1,sizes:d=l?"(max-width: 768px) 100vw, 50vw":void 0,priority:u=!1,className:m="",animate:f=!0,style:h={}}=e,[x,g]=(0,r.useState)(!0),[p,v]=(0,r.useState)(!1);return(0,s.jsxs)("div",{className:"relative overflow-hidden ".concat(m),style:{minHeight:l?"100%":void 0,height:l?"100%":void 0,...h},onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),children:[x&&(0,s.jsx)(i.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),(0,s.jsx)(i.E.div,{className:"w-full h-full",animate:f&&p?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:(0,s.jsx)(n.default,{src:t,alt:a,width:o,height:c,fill:l,sizes:d,priority:u,className:"\n            ".concat(x?"opacity-0":"opacity-100"," \n            transition-opacity duration-500\n            ").concat(l?"object-cover":"","\n          "),onLoad:()=>g(!1)})})]})}}}]);