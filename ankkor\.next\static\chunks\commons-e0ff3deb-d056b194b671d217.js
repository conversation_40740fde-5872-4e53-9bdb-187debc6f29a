"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6628],{94348:function(t,e,o){o.r(e),o.d(e,{addInventoryMapping:function(){return u},clearInventoryMappings:function(){return y},getAllInventoryMappings:function(){return h},getProductHandleFromInventory:function(){return g},loadInventoryMap:function(){return l},saveInventoryMap:function(){return d},updateInventoryMappings:function(){return m}});var r=o(29865),n=o(40257);let a="inventory:mapping:",i=new r.s({url:n.env.UPSTASH_REDIS_REST_URL||n.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:n.env.UPSTASH_REDIS_REST_TOKEN||n.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),c={};function s(){return!!(n.env.UPSTASH_REDIS_REST_URL&&n.env.UPSTASH_REDIS_REST_TOKEN||n.env.NEXT_PUBLIC_KV_REST_API_URL&&n.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function l(){if(!s())return{...c};try{let t=await i.keys("".concat(a,"*"));if(0===t.length)return console.log("No existing inventory mappings found in Redis"),{};let e={},o=await i.mget(...t);return t.forEach((t,r)=>{let n=t.replace(a,""),i=o[r];e[n]=i}),console.log("Loaded inventory mapping with ".concat(Object.keys(e).length," entries from Redis")),e}catch(t){return console.error("Error loading inventory mapping from Redis:",t),console.log("Falling back to in-memory storage"),{...c}}}async function d(t){if(s())try{let e=i.pipeline(),o=await i.keys("".concat(a,"*"));o.length>0&&e.del(...o),Object.entries(t).forEach(t=>{let[o,r]=t;e.set("".concat(a).concat(o),r)}),await e.exec(),console.log("Saved inventory mapping with ".concat(Object.keys(t).length," entries to Redis"))}catch(e){console.error("Error saving inventory mapping to Redis:",e),console.log("Falling back to in-memory storage"),Object.assign(c,t)}else Object.assign(c,t),console.log("Saved inventory mapping with ".concat(Object.keys(t).length," entries to memory"))}async function u(t,e){try{return s()?(await i.set("".concat(a).concat(t),e),console.log("Added mapping to Redis: ".concat(t," -> ").concat(e))):(c[t]=e,console.log("Added mapping to memory: ".concat(t," -> ").concat(e))),!0}catch(o){console.error("Error adding inventory mapping:",o);try{return c[t]=e,console.log("Added mapping to memory fallback: ".concat(t," -> ").concat(e)),!0}catch(t){return console.error("Error adding to memory fallback:",t),!1}}}async function g(t){try{if(s())return await i.get("".concat(a).concat(t))||null;return c[t]||null}catch(e){console.error("Error getting product handle from Redis:",e);try{return c[t]||null}catch(t){return console.error("Error getting from memory fallback:",t),null}}}async function m(t){try{if(s()){let e=i.pipeline();for(let{inventoryItemId:o,productHandle:r}of t)e.set("".concat(a).concat(o),r);await e.exec(),console.log("Updated ".concat(t.length," inventory mappings in Redis"))}else{for(let{inventoryItemId:e,productHandle:o}of t)c[e]=o;console.log("Updated ".concat(t.length," inventory mappings in memory"))}return!0}catch(e){console.error("Error updating inventory mappings in Redis:",e);try{for(let{inventoryItemId:e,productHandle:o}of t)c[e]=o;return console.log("Updated ".concat(t.length," inventory mappings in memory fallback")),!0}catch(t){return console.error("Error updating in memory fallback:",t),!1}}}async function h(){return await l()}async function y(){try{if(s()){let t=await i.keys("".concat(a,"*"));t.length>0&&await i.del(...t),console.log("Cleared all inventory mappings from Redis")}else Object.keys(c).forEach(t=>{delete c[t]}),console.log("Cleared all inventory mappings from memory");return!0}catch(t){return console.error("Error clearing inventory mappings:",t),!1}}},87758:function(t,e,o){o.d(e,{rY:function(){return c}});var r=o(59625),n=o(89134);let a=()=>Math.random().toString(36).substring(2,15),i=async(t,e,o)=>{try{let r=await fetch("/api/products/".concat(t,"/stock").concat(o?"?variation_id=".concat(o):""));if(!r.ok)return{available:!1,message:"Unable to verify stock availability"};let n=await r.json();if("IN_STOCK"!==n.stockStatus&&"instock"!==n.stockStatus)return{available:!1,message:"This product is currently out of stock",stockStatus:n.stockStatus};if(null!==n.stockQuantity&&n.stockQuantity<e)return{available:!1,message:"Only ".concat(n.stockQuantity," items available in stock"),stockQuantity:n.stockQuantity,stockStatus:n.stockStatus};return{available:!0,stockQuantity:n.stockQuantity,stockStatus:n.stockStatus}}catch(t){return console.error("Stock validation error:",t),{available:!0,message:"Stock validation temporarily unavailable"}}},c=(0,r.Ue)()((0,n.tJ)((t,e)=>({items:[],itemCount:0,isLoading:!1,error:null,addToCart:async o=>{t({isLoading:!0,error:null});try{let r=await i(o.productId,o.quantity,o.variationId);if(!r.available)throw Error(r.message||"Product is out of stock");let n=e().items,c=o.price;"string"==typeof c&&(c=c.replace(/[₹$€£]/g,"").trim().replace(/,/g,""));let s={...o,price:c},l=n.findIndex(t=>t.productId===s.productId&&t.variationId===s.variationId);if(-1!==l){let e=[...n];e[l].quantity+=s.quantity,t({items:e,itemCount:e.reduce((t,e)=>t+e.quantity,0),isLoading:!1})}else{let e={...s,id:a()};t({items:[...n,e],itemCount:n.reduce((t,e)=>t+e.quantity,0)+e.quantity,isLoading:!1})}console.log("Item added to cart successfully");try{let t={state:{items:e().items,itemCount:e().itemCount,isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart to localStorage:",t)}}catch(e){console.error("Error adding item to cart:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:(o,r)=>{t({isLoading:!0,error:null});try{let n=e().items;if(r<=0)return e().removeCartItem(o);let a=n.map(t=>t.id===o?{...t,quantity:r}:t);t({items:a,itemCount:a.reduce((t,e)=>t+e.quantity,0),isLoading:!1});try{let t={state:{items:a,itemCount:a.reduce((t,e)=>t+e.quantity,0),isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart update to localStorage:",t)}}catch(e){console.error("Error updating cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:o=>{t({isLoading:!0,error:null});try{let r=e().items.filter(t=>t.id!==o);t({items:r,itemCount:r.reduce((t,e)=>t+e.quantity,0),isLoading:!1});try{let t={state:{items:r,itemCount:r.reduce((t,e)=>t+e.quantity,0),isLoading:!1,error:null},version:1};localStorage.setItem("ankkor-local-cart",JSON.stringify(t))}catch(t){console.warn("Failed to manually persist cart removal to localStorage:",t)}}catch(e){console.error("Error removing cart item:",e),t({error:e instanceof Error?e.message:"An unknown error occurred",isLoading:!1})}},clearCart:()=>{t({items:[],itemCount:0,isLoading:!1,error:null});try{localStorage.setItem("ankkor-local-cart",JSON.stringify({state:{items:[],itemCount:0,isLoading:!1,error:null},version:1}))}catch(t){console.warn("Failed to manually persist cart clearing to localStorage:",t)}},setError:e=>{t({error:e})},setIsLoading:e=>{t({isLoading:e})},subtotal:()=>{let t=e().items;try{let e=t.reduce((t,e)=>{let o=0;if("string"==typeof e.price){let t=e.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");o=parseFloat(t)}else o=e.price;return isNaN(o)?(console.warn("Invalid price for item ".concat(e.id,": ").concat(e.price)),t):t+o*e.quantity},0);return isNaN(e)?0:e}catch(t){return console.error("Error calculating subtotal:",t),0}},total:()=>{let t=e().subtotal();return isNaN(t)?0:t},syncWithWooCommerce:async o=>{let{items:r}=e();if(0===r.length)throw Error("Cart is empty");try{if(console.log("Syncing cart with WooCommerce..."),console.log("Auth token provided:",!!o),t({isLoading:!0}),o){console.log("User is authenticated, using JWT-to-Cookie bridge");try{let e=await s(o,r);return t({isLoading:!1}),e}catch(t){console.error("JWT-to-Cookie bridge failed:",t),console.log("Falling back to guest checkout...")}}console.log("User is not authenticated, redirecting to WooCommerce checkout");let e="".concat("https://maroon-lapwing-781450.hostingersite.com","/checkout/");return console.log("Guest checkout URL:",e),t({isLoading:!1}),e}catch(e){console.error("Error syncing cart with WooCommerce:",e),t({isLoading:!1});try{console.log("Attempting fallback method for cart sync...");let t="".concat("https://maroon-lapwing-781450.hostingersite.com","/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1");return r.forEach((e,o)=>{0===o?t+="&add-to-cart=".concat(e.productId,"&quantity=").concat(e.quantity):t+="&add-to-cart[".concat(o,"]=").concat(e.productId,"&quantity[").concat(o,"]=").concat(e.quantity),e.variationId&&(t+="&variation_id=".concat(e.variationId))}),console.log("Fallback checkout URL:",t),t}catch(t){throw console.error("Fallback method failed:",t),Error("Failed to sync cart with WooCommerce. Please try again or contact support.")}}}}),{name:"ankkor-local-cart",version:1}));async function s(t,e){if(!t)throw Error("Authentication token is required");let o="https://maroon-lapwing-781450.hostingersite.com",r="https://maroon-lapwing-781450.hostingersite.com/checkout/";if(!o||!r)throw Error("WordPress or checkout URL not configured. Check your environment variables.");try{console.log("Creating WordPress session from JWT token..."),console.log("Using endpoint:","".concat(o,"/wp-json/headless/v1/create-wp-session")),console.log("Token length:",t.length),console.log("Token preview:",t.substring(0,20)+"...");let e=await fetch("".concat(o,"/wp-json/headless/v1/create-wp-session"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({token:t}),credentials:"include"});if(console.log("Response status:",e.status),console.log("Response headers:",Object.fromEntries(e.headers.entries())),!e.ok){let t="HTTP ".concat(e.status,": ").concat(e.statusText);try{let o=await e.json();t=o.message||o.code||t,console.error("Error response data:",o)}catch(t){console.error("Could not parse error response:",t)}throw Error("Failed to create WordPress session: ".concat(t))}let n=await e.json();if(console.log("Response data:",n),!n.success)throw Error(n.message||"Failed to create WordPress session");return console.log("WordPress session created successfully"),console.log("Redirecting to checkout URL:",r),r}catch(t){if(console.error("Error creating WordPress session:",t),t instanceof TypeError&&t.message.includes("fetch"))throw Error("Network error: Could not connect to WordPress. Please check your internet connection.");throw Error(t instanceof Error?t.message:"Failed to prepare checkout")}}},82429:function(t,e,o){function r(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"INR";switch(t){case"INR":return"₹";case"USD":return"$";case"EUR":return"€";case"GBP":return"\xa3";default:return t}}o.d(e,{jK:function(){return r}}),o(94348)},92371:function(t,e,o){o.d(e,{Y:function(){return l},x:function(){return s}});var r=o(59625),n=o(89134),a=o(82372);let i={getItem:t=>{try{return localStorage.getItem(t)}catch(t){return console.error("localStorage.getItem error:",t),null}},setItem:(t,e)=>{try{localStorage.setItem(t,e)}catch(t){console.error("localStorage.setItem error:",t)}},removeItem:t=>{try{localStorage.removeItem(t)}catch(t){console.error("localStorage.removeItem error:",t)}}},c=(t,e)=>{try{if(!e||!e.lines){console.error("Invalid normalized cart data",e);return}let o=e.lines.reduce((t,e)=>t+(e.quantity||0),0),r=e.lines.map(t=>{var e;return{id:t.id,variantId:t.merchandise.id,productId:t.merchandise.product.id,title:t.merchandise.product.title,handle:t.merchandise.product.handle,image:(null===(e=t.merchandise.product.image)||void 0===e?void 0:e.url)||"",price:t.merchandise.price,quantity:t.quantity,currencyCode:t.merchandise.currencyCode}});t({items:r,subtotal:e.cost.subtotalAmount.amount,total:e.cost.totalAmount.amount,currencyCode:e.cost.totalAmount.currencyCode,itemCount:o,checkoutUrl:e.checkoutUrl,isLoading:!1})}catch(e){console.error("Error updating cart state:",e),t({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}},s=(0,r.Ue)()((0,n.tJ)((t,e)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>t({isOpen:!0}),closeCart:()=>t({isOpen:!1}),toggleCart:()=>t(t=>({isOpen:!t.isOpen})),initCart:async()=>{let o=e();if(o.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;t({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(o.cartId)try{let e=await (0,a.dv)();if(e)return t({isLoading:!1,initializationInProgress:!1}),e}catch(t){console.log("Existing cart validation failed, creating new cart")}let e=await (0,a.Bk)();if(e&&e.id)return t({cartId:e.id,checkoutUrl:e.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",e.id),e;throw Error("Failed to create cart: No cart ID returned")}catch(e){return console.error("Failed to initialize cart:",e),t({isLoading:!1,initializationInProgress:!1,initializationError:e instanceof Error?e.message:"Unknown error initializing cart"}),null}},addItem:async o=>{t({isLoading:!0});try{if(!o.variantId)throw console.error("Cannot add item to cart: Missing variant ID",o),t({isLoading:!1}),Error("Missing variant ID for item");let r=e().cartId;if(!r){console.log("Cart not initialized, creating a new cart...");let t=await (0,a.Bk)();if(t&&t.id)console.log("New cart created:",t.id),r=t.id;else throw Error("Failed to initialize cart")}if(!r)throw Error("Failed to initialize cart: No cart ID available");console.log("Adding item to cart: ".concat(o.title," (").concat(o.variantId,"), quantity: ").concat(o.quantity));try{let e=await (0,a.Xq)(r,[{merchandiseId:o.variantId,quantity:o.quantity||1}]);if(!e)throw Error("Failed to add item to cart: No cart returned");let n=(0,a.Id)(e);c(t,n),t({isOpen:!0}),console.log("Item added to cart successfully. Cart now has ".concat(n.lines.length," items."))}catch(t){if(console.error("Shopify API error when adding to cart:",t),t instanceof Error)throw Error("Failed to add item to cart: ".concat(t.message));throw Error("Failed to add item to cart: Unknown API error")}}catch(e){throw console.error("Failed to add item to cart:",e),t({isLoading:!1}),e}},updateItem:async(o,r)=>{let n=e();t({isLoading:!0});try{if(!n.cartId)throw Error("Cart not initialized");if(console.log("Updating item in cart: ".concat(o,", new quantity: ").concat(r)),r<=0)return console.log("Quantity is ".concat(r,", removing item from cart")),e().removeItem(o);let i=await (0,a.xu)(n.cartId,[{id:o,quantity:r}]);if(!i)throw Error("Failed to update item: No cart returned");let s=(0,a.Id)(i);c(t,s),console.log("Item updated successfully. Cart now has ".concat(s.lines.length," items."))}catch(e){throw console.error("Failed to update item in cart:",e),t({isLoading:!1}),e}},removeItem:async o=>{let r=e();t({isLoading:!0});try{if(!r.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log("Removing item from cart: ".concat(o));let e=[...r.items],n=e.find(t=>t.id===o);n?console.log('Removing "'.concat(n.title,'" (').concat(n.variantId,") from cart")):console.warn("Item with ID ".concat(o," not found in cart"));let i=await (0,a.h2)(r.cartId,[o]);if(!i)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let s=(0,a.Id)(i),l=s.lines.map(t=>({id:t.id,title:t.merchandise.product.title}));console.log("Cart before removal:",e.length,"items"),console.log("Cart after removal:",l.length,"items"),e.length===l.length&&console.warn("Item count did not change after removal operation"),c(t,s),console.log("Item removed successfully. Cart now has ".concat(s.lines.length," items."))}catch(e){throw console.error("Failed to remove item from cart:",e),t({isLoading:!1}),e}},clearCart:async()=>{e(),t({isLoading:!0});try{console.log("Clearing cart and creating a new one");let e=await (0,a.Bk)();if(!e)throw Error("Failed to create new cart");t({cartId:e.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:e.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",e.id)}catch(e){throw console.error("Failed to clear cart:",e),t({isLoading:!1}),e}}}),{name:"ankkor-cart",storage:(0,n.FL)(()=>i),version:1,partialize:t=>({cartId:t.cartId,items:t.items,subtotal:t.subtotal,total:t.total,currencyCode:t.currencyCode,itemCount:t.itemCount,checkoutUrl:t.checkoutUrl})})),l=(0,r.Ue)()((0,n.tJ)((t,e)=>({items:[],isLoading:!1,addToWishlist:e=>{t(t=>t.items.some(t=>t.id===e.id)?t:{items:[...t.items,e]})},removeFromWishlist:e=>{t(t=>({items:t.items.filter(t=>t.id!==e)}))},clearWishlist:()=>{t({items:[]})},isInWishlist:t=>e().items.some(e=>e.id===t)}),{name:"ankkor-wishlist",storage:(0,n.FL)(()=>i),partialize:t=>({items:t.items})}))},93448:function(t,e,o){o.d(e,{cn:function(){return a}});var r=o(61994),n=o(53335);function a(){for(var t=arguments.length,e=Array(t),o=0;o<t;o++)e[o]=arguments[o];return(0,n.m6)((0,r.W)(e))}}}]);