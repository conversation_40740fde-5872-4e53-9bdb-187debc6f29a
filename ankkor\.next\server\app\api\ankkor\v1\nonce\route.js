"use strict";(()=>{var e={};e.id=483,e.ids=[483],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},26065:(e,n,t)=>{t.r(n),t.d(n,{originalPathname:()=>k,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>h,serverHooks:()=>m,staticGenerationAsyncStorage:()=>f});var r={};t.r(r),t.d(r,{GET:()=>i});var o=t(49303),a=t(88716),c=t(60670),s=t(87070);async function i(e){try{let n="https://maroon-lapwing-781450.hostingersite.com";if(!n)throw Error("WooCommerce URL not configured");let t=e.headers.get("Cart-Token"),r=await u(n,t);if(r||(r=await l(n,t)),r||(r=await p(n,t)),!r)throw Error("Could not obtain a valid nonce from any source");return s.NextResponse.json({nonce:r},{headers:{"Cache-Control":"no-store, max-age=0"}})}catch(e){return console.error("Error fetching WooCommerce nonce:",e),s.NextResponse.json({success:!1,message:e instanceof Error?e.message:"An error occurred fetching the nonce"},{status:500})}}async function u(e,n){try{let t={"Content-Type":"application/json"};n&&(t["Cart-Token"]=n);let r=await fetch(`${e}/wp-json/wc/store/v1/cart`,{method:"GET",headers:t,credentials:"include",cache:"no-store"}),o=r.headers.get("x-wc-store-api-nonce");if(o)return o;let a=await r.json();if(a.extensions&&a.extensions.store_api_nonce)return a.extensions.store_api_nonce;return null}catch(e){return console.error("Error fetching nonce from Store API:",e),null}}async function l(e,n){try{let t={"Content-Type":"application/json"};n&&(t["Cart-Token"]=n);let r=await fetch(`${e}/wp-json`,{method:"GET",headers:t,credentials:"include",cache:"no-store"});if(!r.ok)return null;let o=await r.json();if(o&&o.authentication&&o.authentication.nonce)return o.authentication.nonce;return null}catch(e){return console.error("Error fetching nonce from WP API:",e),null}}async function p(e,n){try{let t={"Content-Type":"application/json"};n&&(t["Cart-Token"]=n);let r=(await fetch(`${e}/wp-json/wc/store/v1/products`,{method:"GET",headers:t,cache:"no-store"})).headers.get("x-wc-store-api-nonce");if(r)return r;return null}catch(e){return console.error("Error fetching nonce from cart request:",e),null}}let h=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/ankkor/v1/nonce/route",pathname:"/api/ankkor/v1/nonce",filename:"route",bundlePath:"app/api/ankkor/v1/nonce/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\ankkor\\v1\\nonce\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:f,serverHooks:m}=h,k="/api/ankkor/v1/nonce/route";function g(){return(0,c.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:f})}}};var n=require("../../../../../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),r=n.X(0,[8948,5972],()=>t(26065));module.exports=r})();