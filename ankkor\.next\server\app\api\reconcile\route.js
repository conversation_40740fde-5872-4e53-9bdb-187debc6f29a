"use strict";(()=>{var e={};e.id=9417,e.ids=[9417],e.modules={45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},80543:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>u,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>y,staticGenerationAsyncStorage:()=>d});var o=r(49303),a=r(88716),s=r(60670),i=r(44343),c=e([i]);i=(c.then?(await c)():c)[0];let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/reconcile/route",pathname:"/api/reconcile",filename:"route",bundlePath:"app/api/reconcile/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\reconcile\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:y}=p,h="/api/reconcile/route";function u(){return(0,s.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:d})}n()}catch(e){n(e)}})},44343:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GET:()=>c,POST:()=>i});var o=r(87070),a=r(5895),s=e([a]);async function i(e){try{let t;let r=e.headers.get("x-api-key"),n=process.env.SHOPIFY_REVALIDATION_SECRET;if(!r||r!==n)return o.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});let{type:s="inventory",handle:i,force:c=!1}=await e.json().catch(()=>({}));switch(s){case"all-products":t=await (0,a.reconcileAllProducts)();break;case"inventory":t=await (0,a.p)(i);break;case"product":if(!i)return o.NextResponse.json({success:!1,error:'Product handle is required for type "product"'},{status:400});t=await (0,a.reconcileProductByHandle)(i);break;default:return o.NextResponse.json({success:!1,error:`Unknown reconciliation type: ${s}`},{status:400})}return o.NextResponse.json({success:!0,type:s,handle:i,result:t},{status:200})}catch(e){return console.error("Error in reconciliation API:",e),o.NextResponse.json({success:!1,error:e.message},{status:500})}}async function c(e){return o.NextResponse.json({status:"Reconciliation API is active"},{status:200})}a=(s.then?(await s)():s)[0],n()}catch(e){n(e)}})},5895:(e,t,r)=>{r.a(e,async(e,n)=>{try{r.d(t,{p:()=>c});var o=r(19910),a=r(92861),s=r(57708),i=e([o]);async function c(){try{console.log("Starting inventory reconciliation...");let e=await o.Xp(),t=await (0,a.Ls)(),r=0,n=0,i=0;for(let o of e){let e=o.id.toString();t[e]?t[e].inventory!==o.stock_quantity?(t[e].inventory=o.stock_quantity||0,r++):n++:(t[e]={wooId:e,inventory:o.stock_quantity||0,sku:o.sku||"",title:o.name||"",lastUpdated:new Date().toISOString()},i++)}return await (0,a.wm)(t),(0,s.revalidatePath)("/product/[slug]"),(0,s.revalidatePath)("/categories"),console.log(`Reconciliation complete: ${r} updated, ${i} added, ${n} unchanged`),{success:!0,stats:{updated:r,added:i,unchanged:n}}}catch(e){return console.error("Error during inventory reconciliation:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}o=(i.then?(await i)():i)[0],n()}catch(e){n(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,5972,4766,4868,7708,9910],()=>r(80543));module.exports=n})();