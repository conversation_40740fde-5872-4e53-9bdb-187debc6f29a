(()=>{var e={};e.id=4122,e.ids=[4122],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},77471:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d}),s(65643),s(52617),s(12523);var a=s(23191),c=s(88716),r=s(37922),l=s.n(r),i=s(95231),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);s.d(t,n);let d=["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65643)),"E:\\ankkorwoo\\ankkor\\src\\app\\admin\\products\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],o=["E:\\ankkorwoo\\ankkor\\src\\app\\admin\\products\\page.tsx"],x="/admin/products/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:c.x.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4139:(e,t,s)=>{Promise.resolve().then(s.bind(s,65135))},941:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},96633:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},31540:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},12714:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(76557).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},65135:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>u});var c=s(10326),r=s(17577),l=s(90434),i=s(15725),n=s(21405),d=s(31540),o=s(88307),x=s(96633),m=s(941),p=s(12714),h=e([i]);function u(){let[e,t]=(0,r.useState)([]),[s,a]=(0,r.useState)(!0),[h,u]=(0,r.useState)(null),[f,j]=(0,r.useState)(""),[v,g]=(0,r.useState)("title"),[N,b]=(0,r.useState)("asc"),[w,y]=(0,r.useState)(null),[k,C]=(0,r.useState)("table"),S=async()=>{a(!0),u(null);try{let e=(await (0,i.Dg)(50)).map(e=>(0,i.Op)(e)).filter(Boolean);t(e)}catch(e){u("Failed to fetch products. Please try again."),console.error("Error fetching products:",e)}finally{a(!1)}},P=e=>{v===e?b("asc"===N?"desc":"asc"):(g(e),b("asc"))},Z=[...e.filter(e=>{let t=f.toLowerCase();return e.title.toLowerCase().includes(t)||e.handle.toLowerCase().includes(t)||e.productType&&e.productType.toLowerCase().includes(t)||e.vendor&&e.vendor.toLowerCase().includes(t)||e.tags&&e.tags.some(e=>e.toLowerCase().includes(t))})].sort((e,t)=>{let s=null,a=null;switch(v){case"title":s=e.title.toLowerCase(),a=t.title.toLowerCase();break;case"price":s=parseFloat(e.price),a=parseFloat(t.price);break;case"productType":s=(e.productType||"").toLowerCase(),a=(t.productType||"").toLowerCase();break;case"vendor":s=(e.vendor||"").toLowerCase(),a=(t.vendor||"").toLowerCase();break;default:s=e[v],a=t[v]}return null===s&&null===a?0:null===s?"asc"===N?-1:1:null===a?"asc"===N?1:-1:s<a?"asc"===N?-1:1:s>a?"asc"===N?1:-1:0});return(0,c.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[c.jsx("h1",{className:"text-2xl font-serif font-bold text-[#2c2c27]",children:"WooCommerce Products"}),(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsxs)("button",{onClick:S,className:"flex items-center px-4 py-2 bg-[#2c2c27] text-white rounded hover:bg-[#3d3d35] transition-colors",children:[c.jsx(n.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,c.jsxs)("button",{onClick:()=>{let t=JSON.stringify(e,null,2),s="data:application/json;charset=utf-8,"+encodeURIComponent(t),a=document.createElement("a");a.setAttribute("href",s),a.setAttribute("download","ankkor-products.json"),a.click()},className:"flex items-center px-4 py-2 bg-[#5c5c52] text-white rounded hover:bg-[#6d6d62] transition-colors",children:[c.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Export JSON"]}),(0,c.jsxs)("button",{onClick:()=>{let t=e.map(e=>[e.id,e.title,e.handle,e.productType||"N/A",e.vendor||"N/A",e.price,e.tags?e.tags.join(", "):"",e.availableForSale?"Yes":"No"]),s=["ID,Title,Handle,Product Type,Vendor,Price,Tags,Available",...t.map(e=>e.map(e=>`"${String(e).replace(/"/g,'""')}"`).join(","))].join("\n"),a="data:text/csv;charset=utf-8,"+encodeURIComponent(s),c=document.createElement("a");c.setAttribute("href",a),c.setAttribute("download","ankkor-products.csv"),c.click()},className:"flex items-center px-4 py-2 bg-[#8a8778] text-white rounded hover:bg-[#9b9889] transition-colors",children:[c.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Export CSV"]})]})]}),c.jsx("div",{className:"mb-6",children:(0,c.jsxs)("div",{className:"relative",children:[c.jsx(o.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a8778]",size:18}),c.jsx("input",{type:"text",placeholder:"Search products...",value:f,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-[#e5e2d9] rounded bg-[#f8f8f5] focus:outline-none focus:ring-2 focus:ring-[#8a8778]"})]})}),(0,c.jsxs)("div",{className:"flex mb-4",children:[c.jsx("button",{onClick:()=>C("table"),className:`px-4 py-2 rounded-l ${"table"===k?"bg-[#2c2c27] text-white":"bg-[#f4f3f0] text-[#2c2c27]"}`,children:"Table View"}),c.jsx("button",{onClick:()=>C("json"),className:`px-4 py-2 rounded-r ${"json"===k?"bg-[#2c2c27] text-white":"bg-[#f4f3f0] text-[#2c2c27]"}`,children:"JSON View"})]}),s?c.jsx("div",{className:"flex justify-center items-center h-64",children:c.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2c2c27]"})}):h?(0,c.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative",role:"alert",children:[c.jsx("strong",{className:"font-bold",children:"Error: "}),c.jsx("span",{className:"block sm:inline",children:h})]}):c.jsx(c.Fragment,{children:"table"===k?c.jsx("div",{className:"overflow-x-auto bg-white rounded-lg shadow",children:(0,c.jsxs)("table",{className:"min-w-full divide-y divide-[#e5e2d9]",children:[c.jsx("thead",{className:"bg-[#f4f3f0]",children:(0,c.jsxs)("tr",{children:[c.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider cursor-pointer",onClick:()=>P("title"),children:(0,c.jsxs)("div",{className:"flex items-center",children:["Title","title"===v&&("asc"===N?c.jsx(x.Z,{size:16}):c.jsx(m.Z,{size:16}))]})}),c.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider cursor-pointer",onClick:()=>P("productType"),children:(0,c.jsxs)("div",{className:"flex items-center",children:["Type","productType"===v&&("asc"===N?c.jsx(x.Z,{size:16}):c.jsx(m.Z,{size:16}))]})}),c.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider cursor-pointer",onClick:()=>P("vendor"),children:(0,c.jsxs)("div",{className:"flex items-center",children:["Vendor","vendor"===v&&("asc"===N?c.jsx(x.Z,{size:16}):c.jsx(m.Z,{size:16}))]})}),c.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider cursor-pointer",onClick:()=>P("price"),children:(0,c.jsxs)("div",{className:"flex items-center",children:["Price","price"===v&&("asc"===N?c.jsx(x.Z,{size:16}):c.jsx(m.Z,{size:16}))]})}),c.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider",children:"Status"}),c.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-[#5c5c52] uppercase tracking-wider",children:"Actions"})]})}),c.jsx("tbody",{className:"bg-white divide-y divide-[#e5e2d9]",children:Z.map(e=>(0,c.jsxs)("tr",{className:"hover:bg-[#f8f8f5]",children:[c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,c.jsxs)("div",{className:"flex items-center",children:[e.images&&e.images.length>0?c.jsx("img",{className:"h-10 w-10 rounded-full object-cover mr-3",src:e.images[0].url,alt:e.title}):c.jsx("div",{className:"h-10 w-10 rounded-full bg-[#e5e2d9] mr-3"}),c.jsx("div",{className:"text-sm font-medium text-[#2c2c27]",children:e.title})]})}),c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("div",{className:"text-sm text-[#5c5c52]",children:e.productType||"N/A"})}),c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("div",{className:"text-sm text-[#5c5c52]",children:e.vendor||"N/A"})}),c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,c.jsxs)("div",{className:"text-sm text-[#5c5c52]",children:["$",parseFloat(e.price).toFixed(2)]})}),c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${e.availableForSale?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.availableForSale?"Active":"Inactive"})}),(0,c.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[c.jsx("button",{onClick:()=>y(e),className:"text-[#8a8778] hover:text-[#5c5c52] mr-3",children:c.jsx(p.Z,{size:18})}),c.jsx(l.default,{href:`/product/${e.handle}`,className:"text-[#2c2c27] hover:text-[#5c5c52]",target:"_blank",children:"View"})]})]},e.id))})]})}):c.jsx("div",{className:"bg-[#f8f8f5] rounded-lg p-4 overflow-auto max-h-[600px]",children:c.jsx("pre",{className:"text-sm text-[#2c2c27]",children:JSON.stringify(Z,null,2)})})}),w&&c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-[#e5e2d9]",children:[c.jsx("h2",{className:"text-xl font-serif font-bold text-[#2c2c27]",children:w.title}),c.jsx("button",{onClick:()=>y(null),className:"text-[#5c5c52] hover:text-[#2c2c27]",children:"\xd7"})]}),(0,c.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,c.jsxs)("div",{children:[w.images&&w.images.length>0?c.jsx("img",{src:w.images[0].url,alt:w.title,className:"w-full h-auto object-cover rounded"}):c.jsx("div",{className:"w-full h-64 bg-[#e5e2d9] rounded flex items-center justify-center",children:c.jsx("span",{className:"text-[#8a8778]",children:"No image available"})}),w.images&&w.images.length>1&&c.jsx("div",{className:"grid grid-cols-4 gap-2 mt-2",children:w.images.slice(0,4).map((e,t)=>c.jsx("img",{src:e.url,alt:`${w.title} - ${t+1}`,className:"w-full h-20 object-cover rounded"},t))})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"mb-4",children:[c.jsx("h3",{className:"text-sm font-medium text-[#8a8778]",children:"Product Details"}),(0,c.jsxs)("div",{className:"mt-2 grid grid-cols-2 gap-2",children:[c.jsx("div",{className:"text-sm text-[#5c5c52]",children:"Handle:"}),c.jsx("div",{className:"text-sm text-[#2c2c27]",children:w.handle}),c.jsx("div",{className:"text-sm text-[#5c5c52]",children:"Type:"}),c.jsx("div",{className:"text-sm text-[#2c2c27]",children:w.productType||"N/A"}),c.jsx("div",{className:"text-sm text-[#5c5c52]",children:"Vendor:"}),c.jsx("div",{className:"text-sm text-[#2c2c27]",children:w.vendor||"N/A"}),c.jsx("div",{className:"text-sm text-[#5c5c52]",children:"Price:"}),(0,c.jsxs)("div",{className:"text-sm text-[#2c2c27]",children:["$",parseFloat(w.price).toFixed(2)]}),c.jsx("div",{className:"text-sm text-[#5c5c52]",children:"Status:"}),c.jsx("div",{className:"text-sm text-[#2c2c27]",children:w.availableForSale?"Active":"Inactive"})]})]}),w.tags&&w.tags.length>0&&(0,c.jsxs)("div",{className:"mb-4",children:[c.jsx("h3",{className:"text-sm font-medium text-[#8a8778]",children:"Tags"}),c.jsx("div",{className:"mt-2 flex flex-wrap gap-1",children:w.tags.map((e,t)=>c.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#f4f3f0] text-[#5c5c52]",children:e},t))})]}),w.options&&w.options.length>0&&(0,c.jsxs)("div",{className:"mb-4",children:[c.jsx("h3",{className:"text-sm font-medium text-[#8a8778]",children:"Options"}),c.jsx("div",{className:"mt-2",children:w.options.map((e,t)=>(0,c.jsxs)("div",{className:"mb-2",children:[c.jsx("div",{className:"text-sm font-medium text-[#2c2c27]",children:e.name}),c.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:e.values.map((e,t)=>c.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#f4f3f0] text-[#5c5c52]",children:e},t))})]},t))})]})]})]}),(0,c.jsxs)("div",{className:"mt-6",children:[c.jsx("h3",{className:"text-sm font-medium text-[#8a8778]",children:"Description"}),c.jsx("div",{className:"mt-2 text-sm text-[#2c2c27] prose max-w-none",dangerouslySetInnerHTML:{__html:w.descriptionHtml||w.description||"No description available."}})]}),w.variants&&w.variants.length>0&&(0,c.jsxs)("div",{className:"mt-6",children:[(0,c.jsxs)("h3",{className:"text-sm font-medium text-[#8a8778]",children:["Variants (",w.variants.length,")"]}),c.jsx("div",{className:"mt-2 overflow-x-auto",children:(0,c.jsxs)("table",{className:"min-w-full divide-y divide-[#e5e2d9]",children:[c.jsx("thead",{className:"bg-[#f4f3f0]",children:(0,c.jsxs)("tr",{children:[c.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider",children:"Title"}),c.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider",children:"Price"}),c.jsx("th",{scope:"col",className:"px-4 py-2 text-left text-xs font-medium text-[#5c5c52] uppercase tracking-wider",children:"Available"})]})}),c.jsx("tbody",{className:"bg-white divide-y divide-[#e5e2d9]",children:w.variants.map((e,t)=>(0,c.jsxs)("tr",{className:"hover:bg-[#f8f8f5]",children:[c.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-[#2c2c27]",children:e.title}),(0,c.jsxs)("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-[#5c5c52]",children:["$",parseFloat(e.price).toFixed(2)]}),c.jsx("td",{className:"px-4 py-2 whitespace-nowrap",children:c.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${e.availableForSale?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.availableForSale?"Yes":"No"})})]},t))})]})})]})]}),c.jsx("div",{className:"p-6 border-t border-[#e5e2d9] flex justify-end",children:c.jsx("button",{onClick:()=>y(null),className:"px-4 py-2 bg-[#f4f3f0] text-[#2c2c27] rounded hover:bg-[#e5e2d9] transition-colors",children:"Close"})})]})})]})}i=(h.then?(await h)():h)[0],a()}catch(e){a(e)}})},65643:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\admin\products\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1056,8702],()=>s(77471));module.exports=a})();