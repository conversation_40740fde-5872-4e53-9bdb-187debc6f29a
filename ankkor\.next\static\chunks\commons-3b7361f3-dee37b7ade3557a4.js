"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5363],{87466:function(t,r,e){e.d(r,{xh:function(){return l}});var o=e(29865),i=e(40257);let n=new o.s({url:i.env.UPSTASH_REDIS_REST_URL||i.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:i.env.UPSTASH_REDIS_REST_TOKEN||i.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),a={},c={};function d(){return!!(i.env.UPSTASH_REDIS_REST_URL&&i.env.UPSTASH_REDIS_REST_TOKEN||i.env.NEXT_PUBLIC_KV_REST_API_URL&&i.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function s(t){try{if(d())return await n.get("".concat("woo:inventory:mapping:").concat(t))||null;return a[t]||null}catch(r){console.error("Error getting product slug from Redis:",r);try{return a[t]||null}catch(t){return console.error("Error getting from memory fallback:",t),null}}}async function u(t){try{if(d())return await n.hget("shopify:to:woo:mapping",t)||null;return c[t]||null}catch(r){console.error("Error getting WooCommerce ID for Shopify ID ".concat(t,":"),r);try{return c[t]||null}catch(t){return console.error("Error getting from memory fallback:",t),null}}}async function l(t){if(!t||"undefined"===t||"null"===t)return console.warn("Invalid product ID received:",t),t;if(t.includes("gid://shopify/Product/")){console.log("Detected Shopify ID: ".concat(t,", attempting to map to WooCommerce ID"));let r=await u(t);return r?(console.log("Mapped Shopify ID ".concat(t," to WooCommerce ID ").concat(r)),r):(console.warn("No mapping found for Shopify ID: ".concat(t,", using original ID")),t)}return t.includes("=")?await s(t)||console.warn("Product ID ".concat(t," not found in inventory mapping, using as is")):/^\d+$/.test(t),t}},67111:function(t,r,e){e.d(r,{xS:function(){return a}});var o=e(59625),i=e(89134),n=e(82372);let a=(0,o.Ue)()((0,i.tJ)((t,r)=>({id:null,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1,error:null,initializeCart:async()=>{t({isLoading:!0,error:null});try{let r=await n.dv();if(r){let e=n.Id(r);t({id:e.id,items:e.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:e.totalQuantity,subtotal:e.cost.subtotalAmount.amount,total:e.cost.totalAmount.amount,isLoading:!1});return}let e=await n.Bk();if(e){let r=n.Id(e);t({id:r.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(r){console.error("Error initializing cart:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},addToCart:async e=>{t({isLoading:!0,error:null});try{r().id||await r().initializeCart();let o=[{productId:e.productId,quantity:e.quantity,variationId:e.variationId}],i=await n.Xq("",o);if(i){let r=n.Id(i);t({items:r.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to add item to cart")}catch(r){console.error("Error adding item to cart:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:async(e,o)=>{t({isLoading:!0,error:null});try{if(!r().id)throw Error("Cart not initialized");if(o<=0)return r().removeCartItem(e);let i=await n.xu([{key:e,quantity:o}]);if(i){let r=n.Id(i);t({items:r.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to update cart item")}catch(r){console.error("Error updating cart item:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:async e=>{t({isLoading:!0,error:null});try{if(!r().id)throw Error("Cart not initialized");let o=await n.h2("",[e]);if(o){let r=n.Id(o);t({items:r.lines.map(t=>({id:t.id,productId:t.merchandise.product.id,variationId:t.merchandise.id!==t.merchandise.product.id?t.merchandise.id:void 0,quantity:t.quantity,name:t.merchandise.title,price:t.cost.totalAmount.amount,image:t.merchandise.product.image,attributes:t.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to remove item from cart")}catch(r){console.error("Error removing cart item:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},clearCart:async()=>{t({isLoading:!0,error:null});try{let r=await n.Bk();if(r){let e=n.Id(r);t({id:e.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(r){console.error("Error clearing cart:",r),t({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},setError:r=>t({error:r}),setIsLoading:r=>t({isLoading:r})}),{name:"woo-cart-storage",version:1,partialize:t=>({id:t.id}),onRehydrateStorage:()=>t=>{t&&t.id&&t.initializeCart()}}))}}]);