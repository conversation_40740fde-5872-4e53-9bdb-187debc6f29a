"use strict";(()=>{var e={};e.id=7520,e.ids=[7520],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},37656:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>l,patchFetch:()=>m,requestAsyncStorage:()=>u,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var t={};r.r(t),r.d(t,{GET:()=>c});var n=r(49303),s=r(88716),a=r(60670),i=r(87070);async function c(e){try{let o="https://maroon-lapwing-781450.hostingersite.com";if(!o)throw Error("WooCommerce URL not configured");let r=e.headers.get("Cart-Token"),t=await fetch(`${o}/wp-json/wc/store/v1/cart`,{method:"GET",headers:{"Content-Type":"application/json",...r?{"Cart-Token":r}:{}},credentials:"include",cache:"no-store"});if(!t.ok)throw Error(`Failed to fetch nonce: ${t.status}`);let n=t.headers.get("X-WC-Store-API-Nonce");if(!n){let e=await t.json();e.extensions&&e.extensions.store_api_nonce&&(n=e.extensions.store_api_nonce)}if(!n)throw Error("No nonce returned from WooCommerce");return i.NextResponse.json({nonce:n},{headers:{"Cache-Control":"no-store, max-age=0"}})}catch(e){return console.error("Error fetching WooCommerce nonce:",e),i.NextResponse.json({success:!1,message:e instanceof Error?e.message:"An error occurred fetching the nonce"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/nonce/route",pathname:"/api/nonce",filename:"route",bundlePath:"app/api/nonce/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\nonce\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:u,staticGenerationAsyncStorage:d,serverHooks:h}=p,l="/api/nonce/route";function m(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}}};var o=require("../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[8948,5972],()=>r(37656));module.exports=t})();