{"/about/page": "/about", "/_not-found/page": "/_not-found", "/admin/products/page": "/admin/products", "/about/craftsmanship/page": "/about/craftsmanship", "/about/sustainability/page": "/about/sustainability", "/api/ankkor/v1/nonce/route": "/api/ankkor/v1/nonce", "/api/auth/me/route": "/api/auth/me", "/api/auth/route": "/api/auth", "/api/admin/migrate-inventory-mappings/route": "/api/admin/migrate-inventory-mappings", "/api/auth/update-profile/route": "/api/auth/update-profile", "/api/checkout/route": "/api/checkout", "/api/auth/user/route": "/api/auth/user", "/api/cache/products/[handle]/route": "/api/cache/products/[handle]", "/api/graphql/route": "/api/graphql", "/api/products/[id]/stock/route": "/api/products/[id]/stock", "/api/nonce/route": "/api/nonce", "/api/cron/inventory-sync/route": "/api/cron/inventory-sync", "/api/debug/route": "/api/debug", "/api/razorpay/verify-payment/route": "/api/razorpay/verify-payment", "/api/products/validate-stock/route": "/api/products/validate-stock", "/api/razorpay/create-order/route": "/api/razorpay/create-order", "/api/shipping-rates/route": "/api/shipping-rates", "/api/revalidate/route": "/api/revalidate", "/api/reconcile/route": "/api/reconcile", "/api/webhooks/inventory/route": "/api/webhooks/inventory", "/api/test/route": "/api/test", "/api/user/wishlist/route": "/api/user/wishlist", "/api/webhooks/route": "/api/webhooks", "/cart-test/page": "/cart-test", "/api/trigger-test/route": "/api/trigger-test", "/checkout/page": "/checkout", "/collection/page": "/collection", "/collection/shirts/page": "/collection/shirts", "/category/[slug]/page": "/category/[slug]", "/collection/polos/page": "/collection/polos", "/customer-service/faq/page": "/customer-service/faq", "/customer-service/size-guide/page": "/customer-service/size-guide", "/customer-service/contact/page": "/customer-service/contact", "/customer-service/page": "/customer-service", "/order-confirmed/page": "/order-confirmed", "/page": "/", "/privacy-policy/page": "/privacy-policy", "/local-cart-test/page": "/local-cart-test", "/robots.txt/route": "/robots.txt", "/return-policy/page": "/return-policy", "/product/[slug]/page": "/product/[slug]", "/search/page": "/search", "/shipping-policy/page": "/shipping-policy", "/test-auth/page": "/test-auth", "/terms-of-service/page": "/terms-of-service", "/test/page": "/test", "/test-auth/success/page": "/test-auth/success", "/woocommerce-checkout-test/page": "/woocommerce-checkout-test", "/wishlist/page": "/wishlist", "/woocommerce-cart-test/page": "/woocommerce-cart-test", "/sitemap.xml/route": "/sitemap.xml", "/woocommerce-test/page": "/woocommerce-test", "/woocommerce-test/success/page": "/woocommerce-test/success", "/account/page": "/account", "/api/products/route": "/api/products", "/api/woo-sync/route": "/api/woo-sync", "/categories/page": "/categories", "/sign-up/page": "/sign-up", "/sign-in/page": "/sign-in"}